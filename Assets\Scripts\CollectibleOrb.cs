using UnityEngine;
using System.Collections;

public class CollectibleOrb : MonoBehaviour
{
    [Header("Collection Settings")]
    public int pointValue = 10;
    public float collectionRadius = 1.5f;
    
    [Header("Visual Effects")]
    public GameObject explosionEffect;
    public ParticleSystem glowParticles;
    public ParticleSystem collectionBurst;
    
    [Header("Audio")]
    public AudioClip collectionSound;
    
    [<PERSON><PERSON>("Glow Settings")]
    public Light orbLight;
    public float glowIntensity = 2f;
    public float glowPulseSpeed = 3f;
    
    private AudioSource audioSource;
    private Renderer orbRenderer;
    private Material orbMaterial;
    private bool isCollected = false;
    private float originalLightIntensity;
    
    // Events
    public static System.Action<int> OnOrbCollected;
    
    void Start()
    {
        // Get components
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null)
        {
            audioSource = gameObject.AddComponent<AudioSource>();
        }
        
        orbRenderer = GetComponent<Renderer>();
        if (orbRenderer != null)
        {
            orbMaterial = orbRenderer.material;
        }
        
        // Setup light
        if (orbLight != null)
        {
            originalLightIntensity = orbLight.intensity;
        }
        
        // Start glow particles if available
        if (glowParticles != null)
        {
            glowParticles.Play();
        }
    }
    
    void Update()
    {
        if (isCollected) return;
        
        // Pulse the light intensity
        if (orbLight != null)
        {
            float pulse = Mathf.Sin(Time.time * glowPulseSpeed) * 0.3f + 0.7f;
            orbLight.intensity = originalLightIntensity * pulse * glowIntensity;
        }
        
        // Pulse the material emission if it has one
        if (orbMaterial != null && orbMaterial.HasProperty("_EmissionColor"))
        {
            float pulse = Mathf.Sin(Time.time * glowPulseSpeed) * 0.2f + 0.8f;
            Color baseEmission = Color.magenta; // Violet color
            orbMaterial.SetColor("_EmissionColor", baseEmission * pulse);
        }
    }
    
    void OnTriggerEnter(Collider other)
    {
        if (isCollected) return;
        
        if (other.CompareTag("Player"))
        {
            CollectOrb();
        }
    }
    
    void CollectOrb()
    {
        if (isCollected) return;
        
        isCollected = true;
        
        // Play collection sound
        if (collectionSound != null && audioSource != null)
        {
            audioSource.PlayOneShot(collectionSound);
        }
        
        // Trigger collection burst effect
        if (collectionBurst != null)
        {
            collectionBurst.transform.SetParent(null); // Detach from orb
            collectionBurst.Play();
            
            // Destroy the burst effect after it finishes
            Destroy(collectionBurst.gameObject, collectionBurst.main.duration + collectionBurst.main.startLifetime.constantMax);
        }
        
        // Spawn explosion effect
        if (explosionEffect != null)
        {
            GameObject explosion = Instantiate(explosionEffect, transform.position, Quaternion.identity);
            Destroy(explosion, 3f); // Clean up after 3 seconds
        }
        
        // Notify other systems about collection
        OnOrbCollected?.Invoke(pointValue);
        
        // Start collection animation
        StartCoroutine(CollectionAnimation());
    }
    
    IEnumerator CollectionAnimation()
    {
        float animationTime = 0.5f;
        float elapsed = 0f;
        
        Vector3 originalScale = transform.localScale;
        Vector3 originalPosition = transform.position;
        
        // Stop glow particles
        if (glowParticles != null)
        {
            glowParticles.Stop();
        }
        
        while (elapsed < animationTime)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / animationTime;
            
            // Scale up and fade out
            transform.localScale = Vector3.Lerp(originalScale, originalScale * 1.5f, progress);
            transform.position = Vector3.Lerp(originalPosition, originalPosition + Vector3.up * 2f, progress);
            
            // Fade out the material
            if (orbRenderer != null)
            {
                Color color = orbRenderer.material.color;
                color.a = Mathf.Lerp(1f, 0f, progress);
                orbRenderer.material.color = color;
            }
            
            // Fade out the light
            if (orbLight != null)
            {
                orbLight.intensity = Mathf.Lerp(originalLightIntensity * glowIntensity, 0f, progress);
            }
            
            yield return null;
        }
        
        // Destroy the orb
        Destroy(gameObject);
    }
    
    void OnDrawGizmosSelected()
    {
        // Draw collection radius in editor
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, collectionRadius);
    }
}
