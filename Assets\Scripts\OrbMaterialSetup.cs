using UnityEngine;

[System.Serializable]
public class OrbVisualSettings
{
    [Header("Colors")]
    public Color baseColor = new Color(0.8f, 0.2f, 1f, 0.8f); // Violet with transparency
    public Color emissionColor = new Color(1f, 0.4f, 1f, 1f); // Bright violet emission
    
    [Header("Material Properties")]
    [Range(0f, 1f)]
    public float metallic = 0.1f;
    [Range(0f, 1f)]
    public float smoothness = 0.9f;
    [Range(0f, 5f)]
    public float emissionIntensity = 2f;
    
    [Header("Transparency")]
    [Range(0f, 1f)]
    public float alpha = 0.8f;
}

public class OrbMaterialSetup : MonoBehaviour
{
    [Header("Visual Settings")]
    public OrbVisualSettings visualSettings = new OrbVisualSettings();
    
    [Header("Auto Setup")]
    public bool setupOnStart = true;
    public bool createMaterialInstance = true;
    
    private Renderer orbRenderer;
    private Material orbMaterial;
    
    void Start()
    {
        if (setupOnStart)
        {
            SetupOrbMaterial();
        }
    }
    
    [ContextMenu("Setup Orb Material")]
    public void SetupOrbMaterial()
    {
        orbRenderer = GetComponent<Renderer>();
        if (orbRenderer == null)
        {
            Debug.LogWarning("No Renderer found on " + gameObject.name);
            return;
        }
        
        // Create material instance if needed
        if (createMaterialInstance)
        {
            orbMaterial = new Material(orbRenderer.material);
            orbRenderer.material = orbMaterial;
        }
        else
        {
            orbMaterial = orbRenderer.material;
        }
        
        // Configure material for glowing effect
        ConfigureMaterial();
    }
    
    void ConfigureMaterial()
    {
        if (orbMaterial == null) return;
        
        // Set base color
        if (orbMaterial.HasProperty("_BaseColor"))
        {
            Color baseColor = visualSettings.baseColor;
            baseColor.a = visualSettings.alpha;
            orbMaterial.SetColor("_BaseColor", baseColor);
        }
        else if (orbMaterial.HasProperty("_Color"))
        {
            Color baseColor = visualSettings.baseColor;
            baseColor.a = visualSettings.alpha;
            orbMaterial.SetColor("_Color", baseColor);
        }
        
        // Set emission
        if (orbMaterial.HasProperty("_EmissionColor"))
        {
            Color emission = visualSettings.emissionColor * visualSettings.emissionIntensity;
            orbMaterial.SetColor("_EmissionColor", emission);
            orbMaterial.EnableKeyword("_EMISSION");
        }
        
        // Set metallic and smoothness
        if (orbMaterial.HasProperty("_Metallic"))
        {
            orbMaterial.SetFloat("_Metallic", visualSettings.metallic);
        }
        
        if (orbMaterial.HasProperty("_Smoothness"))
        {
            orbMaterial.SetFloat("_Smoothness", visualSettings.smoothness);
        }
        
        // Enable transparency if needed
        if (visualSettings.alpha < 1f)
        {
            SetMaterialTransparent();
        }
    }
    
    void SetMaterialTransparent()
    {
        if (orbMaterial == null) return;
        
        // Set rendering mode to transparent (for URP/Built-in)
        if (orbMaterial.HasProperty("_Surface"))
        {
            orbMaterial.SetFloat("_Surface", 1); // Transparent
            orbMaterial.SetFloat("_Blend", 0); // Alpha blend
        }
        else
        {
            // For built-in render pipeline
            orbMaterial.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            orbMaterial.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            orbMaterial.SetInt("_ZWrite", 0);
            orbMaterial.DisableKeyword("_ALPHATEST_ON");
            orbMaterial.EnableKeyword("_ALPHABLEND_ON");
            orbMaterial.DisableKeyword("_ALPHAPREMULTIPLY_ON");
            orbMaterial.renderQueue = 3000;
        }
    }
    
    // Method to update colors at runtime
    public void UpdateColors(Color newBaseColor, Color newEmissionColor)
    {
        visualSettings.baseColor = newBaseColor;
        visualSettings.emissionColor = newEmissionColor;
        
        if (orbMaterial != null)
        {
            ConfigureMaterial();
        }
    }
    
    // Method to pulse the emission intensity
    public void PulseEmission(float pulseSpeed = 3f, float minIntensity = 1f, float maxIntensity = 3f)
    {
        if (orbMaterial == null) return;
        
        float pulse = Mathf.Sin(Time.time * pulseSpeed) * 0.5f + 0.5f;
        float intensity = Mathf.Lerp(minIntensity, maxIntensity, pulse);
        
        Color emission = visualSettings.emissionColor * intensity;
        orbMaterial.SetColor("_EmissionColor", emission);
    }
}
