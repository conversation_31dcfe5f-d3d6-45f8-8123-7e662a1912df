using UnityEngine;
using System.Collections;

public class OrbExplosionEffect : MonoBehaviour
{
    [Header("Particle Systems")]
    public ParticleSystem sparklesBurst;
    public ParticleSystem lightRays;
    public ParticleSystem energyWave;
    public ParticleSystem magicalParticles;
    
    [Header("Light Flash")]
    public Light explosionLight;
    public float lightFlashDuration = 0.3f;
    public float maxLightIntensity = 5f;
    public Color lightColor = Color.magenta;
    
    [Header("Screen Shake")]
    public bool enableScreenShake = true;
    public float shakeIntensity = 0.1f;
    public float shakeDuration = 0.2f;
    
    void Start()
    {
        // Setup explosion light
        if (explosionLight != null)
        {
            explosionLight.color = lightColor;
            explosionLight.intensity = 0f;
        }
        
        // Start the explosion sequence
        StartCoroutine(ExplosionSequence());
    }
    
    IEnumerator ExplosionSequence()
    {
        // Play all particle effects
        if (sparklesBurst != null) sparklesBurst.Play();
        if (lightRays != null) lightRays.Play();
        if (energyWave != null) energyWave.Play();
        if (magicalParticles != null) magicalParticles.Play();
        
        // Light flash effect
        if (explosionLight != null)
        {
            StartCoroutine(LightFlash());
        }
        
        // Screen shake effect
        if (enableScreenShake)
        {
            StartCoroutine(ScreenShake());
        }
        
        yield return null;
    }
    
    IEnumerator LightFlash()
    {
        float elapsed = 0f;
        
        while (elapsed < lightFlashDuration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / lightFlashDuration;
            
            // Flash intensity curve (quick rise, slow fall)
            float intensity;
            if (progress < 0.1f)
            {
                intensity = Mathf.Lerp(0f, maxLightIntensity, progress / 0.1f);
            }
            else
            {
                intensity = Mathf.Lerp(maxLightIntensity, 0f, (progress - 0.1f) / 0.9f);
            }
            
            explosionLight.intensity = intensity;
            yield return null;
        }
        
        explosionLight.intensity = 0f;
    }
    
    IEnumerator ScreenShake()
    {
        Camera mainCamera = Camera.main;
        if (mainCamera == null) yield break;
        
        Vector3 originalPosition = mainCamera.transform.position;
        float elapsed = 0f;
        
        while (elapsed < shakeDuration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / shakeDuration;
            
            // Shake intensity decreases over time
            float currentIntensity = shakeIntensity * (1f - progress);
            
            // Random shake offset
            Vector3 shakeOffset = new Vector3(
                Random.Range(-currentIntensity, currentIntensity),
                Random.Range(-currentIntensity, currentIntensity),
                0f
            );
            
            mainCamera.transform.position = originalPosition + shakeOffset;
            yield return null;
        }
        
        // Reset camera position
        mainCamera.transform.position = originalPosition;
    }
    
    // Call this method to create an explosion at a specific position
    public static void CreateExplosion(Vector3 position, GameObject explosionPrefab = null)
    {
        if (explosionPrefab != null)
        {
            GameObject explosion = Instantiate(explosionPrefab, position, Quaternion.identity);
            
            // Auto-destroy after all effects are done
            float maxDuration = 3f;
            Destroy(explosion, maxDuration);
        }
    }
}
