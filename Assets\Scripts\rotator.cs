
using UnityEngine;

public class Rotator : Mono<PERSON>eh<PERSON>our
{
    [Head<PERSON>("Rotation Settings")]
    public Vector3 rotationSpeed = new Vector3(0, 50, 0);
    
    [Header("Bobbing Motion")]
    public float bobbingHeight = 0.5f;
    public float bobbingSpeed = 2f;

    private Vector3 startPosition;

    void Start()
    {
        startPosition = transform.position;
    }

    void Update()
    {
        // Rotate the orb
        transform.Rotate(rotationSpeed * Time.deltaTime);

        // Create bobbing motion
        float newY = startPosition.y + Mathf.Sin(Time.time * bobbingSpeed) * bobbingHeight;
        transform.position = new Vector3(startPosition.x, newY, startPosition.z);
    }
}
