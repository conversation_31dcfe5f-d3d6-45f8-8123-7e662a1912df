Using pre-set license
Built from '6000.0/respin/6000.0.51f1-a206c6c19c75' branch; Version is '6000.0.51f1 (01c3ff5872c5) revision 115711'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'fr' Physical Memory: 7877 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-07T08:26:56Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.51f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
C:/Users/<USER>/Desktop/RollBall
-logFile
Logs/AssetImportWorker2.log
-srvPort
49841
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Users/<USER>/Desktop/RollBall
C:/Users/<USER>/Desktop/RollBall
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [5576]  Target information:

Player connection [5576]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 735075731 [EditorId] 735075731 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-P1RI1CO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [5576] Host joined multi-casting on [***********:54997]...
Player connection [5576] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 9.27 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.51f1 (01c3ff5872c5)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Desktop/RollBall/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: Intel(R) UHD Graphics 770 (ID=0x4690)
    Vendor:   Intel
    VRAM:     3938 MB
    Driver:   32.0.101.6078
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56976
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.004983 seconds.
- Loaded All Assemblies, in  0.505 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 199 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.519 seconds
Domain Reload Profiling: 1023ms
	BeginReloadAssembly (213ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (54ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (57ms)
	LoadAllAssembliesAndSetupDomain (166ms)
		LoadAssemblies (210ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (160ms)
			TypeCache.Refresh (158ms)
				TypeCache.ScanAssembly (145ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (520ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (484ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (297ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (43ms)
			ProcessInitializeOnLoadAttributes (91ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.699 seconds
Refreshing native plugins compatible for Editor in 0.91 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.616 seconds
Domain Reload Profiling: 1313ms
	BeginReloadAssembly (141ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (20ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (480ms)
		LoadAssemblies (386ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (179ms)
			TypeCache.Refresh (133ms)
				TypeCache.ScanAssembly (120ms)
			BuildScriptInfoCaches (36ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (616ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (461ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (286ms)
			ProcessInitializeOnLoadMethodAttributes (72ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 2.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 198 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5978 unused Assets / (6.6 MB). Loaded Objects now: 6628.
Memory consumption went from 154.9 MB to 148.3 MB.
Total: 11.329200 ms (FindLiveObjects: 1.120300 ms CreateObjectMapping: 0.383900 ms MarkObjects: 5.426600 ms  DeleteObjects: 4.397100 ms)

========================================================================
Received Import Request.
  Time since last request: 3986.498578 seconds.
  path: Assets/Materials/Scripts
  artifactKey: Guid(54846683d693c43478834bd84762b871) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Scripts using Guid(54846683d693c43478834bd84762b871) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd2da57e846f2d2dafe0d1f7d62e1c0a6') in 0.0067245 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.938177 seconds.
  path: Assets/Materials/Scripts
  artifactKey: Guid(54846683d693c43478834bd84762b871) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Materials/Scripts using Guid(54846683d693c43478834bd84762b871) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a27ad82c81a92b603bb5882fac16337e') in 0.1349101 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 39.684106 seconds.
  path: Assets
  artifactKey: Guid(00000000000000001000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets using Guid(00000000000000001000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c391582d739b6e62a5220f35cbb167ac') in 0.0009703 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 10.843113 seconds.
  path: Assets/Scripts
  artifactKey: Guid(6b64fad5371208b4ea477623f9a67a61) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts using Guid(6b64fad5371208b4ea477623f9a67a61) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '62cf1456f4fea3ca6c25005be4027f16') in 0.0008372 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.941628 seconds.
  path: Assets/Scripts
  artifactKey: Guid(6b64fad5371208b4ea477623f9a67a61) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts using Guid(6b64fad5371208b4ea477623f9a67a61) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1ad56daebf5b500ad03a4db7e3d531ad') in 0.011626 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 0.75 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 32 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5968 unused Assets / (6.6 MB). Loaded Objects now: 6631.
Memory consumption went from 129.3 MB to 122.7 MB.
Total: 8.925000 ms (FindLiveObjects: 0.479800 ms CreateObjectMapping: 0.385400 ms MarkObjects: 5.060700 ms  DeleteObjects: 2.997900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 204.312198 seconds.
  path: Assets/Scripts/PlayerController.cs
  artifactKey: Guid(521e51cdece08be4a9ba90aa99f267db) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/PlayerController.cs using Guid(521e51cdece08be4a9ba90aa99f267db) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'de27fa07c5f28678271b76d770e108c1') in 0.0097107 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.312897 seconds.
  path: Assets/Scripts/PlayerController.cs
  artifactKey: Guid(521e51cdece08be4a9ba90aa99f267db) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scripts/PlayerController.cs using Guid(521e51cdece08be4a9ba90aa99f267db) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '75108c5952c7c756604e8347b3090161') in 0.0397746 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.652 seconds
Refreshing native plugins compatible for Editor in 0.54 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.766 seconds
Domain Reload Profiling: 1420ms
	BeginReloadAssembly (207ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (58ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (377ms)
		LoadAssemblies (298ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (168ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (141ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (767ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (607ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (125ms)
			ProcessInitializeOnLoadAttributes (417ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (5.7 MB). Loaded Objects now: 6647.
Memory consumption went from 145.4 MB to 139.7 MB.
Total: 6.774500 ms (FindLiveObjects: 0.462500 ms CreateObjectMapping: 0.410900 ms MarkObjects: 3.467400 ms  DeleteObjects: 2.432600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 36.681429 seconds.
  path: Assets/Scripts/PlayerController.cs
  artifactKey: Guid(521e51cdece08be4a9ba90aa99f267db) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/PlayerController.cs using Guid(521e51cdece08be4a9ba90aa99f267db) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a353789c52a58ad65a6c029dafe1aa17') in 0.0024109 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 3296.638752 seconds.
  path: Assets/Settings/DefaultVolumeProfile.asset
  artifactKey: Guid(ab09877e2e707104187f6f83e2f62510) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/DefaultVolumeProfile.asset using Guid(ab09877e2e707104187f6f83e2f62510) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '10528149371aefc32227726816d72a59') in 0.2156613 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.16 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5967 unused Assets / (6.0 MB). Loaded Objects now: 6648.
Memory consumption went from 143.7 MB to 137.7 MB.
Total: 201.887000 ms (FindLiveObjects: 3.207100 ms CreateObjectMapping: 0.463600 ms MarkObjects: 194.451000 ms  DeleteObjects: 3.763200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 0.61 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5967 unused Assets / (6.5 MB). Loaded Objects now: 6648.
Memory consumption went from 143.7 MB to 137.2 MB.
Total: 9.066400 ms (FindLiveObjects: 0.872800 ms CreateObjectMapping: 0.438200 ms MarkObjects: 5.018800 ms  DeleteObjects: 2.735200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.11 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5967 unused Assets / (6.4 MB). Loaded Objects now: 6648.
Memory consumption went from 143.6 MB to 137.2 MB.
Total: 11.473900 ms (FindLiveObjects: 0.632100 ms CreateObjectMapping: 0.647500 ms MarkObjects: 5.765800 ms  DeleteObjects: 4.427000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 0.57 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5967 unused Assets / (6.5 MB). Loaded Objects now: 6648.
Memory consumption went from 143.6 MB to 137.1 MB.
Total: 9.307900 ms (FindLiveObjects: 0.494800 ms CreateObjectMapping: 0.440600 ms MarkObjects: 5.294200 ms  DeleteObjects: 3.077500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 0.92 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5967 unused Assets / (7.2 MB). Loaded Objects now: 6648.
Memory consumption went from 143.6 MB to 136.5 MB.
Total: 9.769400 ms (FindLiveObjects: 0.458200 ms CreateObjectMapping: 0.394300 ms MarkObjects: 5.334500 ms  DeleteObjects: 3.580900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  3.325 seconds
Refreshing native plugins compatible for Editor in 0.60 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.669 seconds
Domain Reload Profiling: 3997ms
	BeginReloadAssembly (1015ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (58ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (250ms)
	RebuildCommonClasses (118ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (2150ms)
		LoadAssemblies (2450ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (219ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (173ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (670ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (514ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (117ms)
			ProcessInitializeOnLoadAttributes (328ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.77 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (6.0 MB). Loaded Objects now: 6650.
Memory consumption went from 143.5 MB to 137.4 MB.
Total: 12.663100 ms (FindLiveObjects: 1.188300 ms CreateObjectMapping: 0.773000 ms MarkObjects: 5.551400 ms  DeleteObjects: 5.147800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.676 seconds
Refreshing native plugins compatible for Editor in 0.57 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.869 seconds
Domain Reload Profiling: 1548ms
	BeginReloadAssembly (214ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (402ms)
		LoadAssemblies (368ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (144ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (125ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (870ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (706ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (506ms)
			ProcessInitializeOnLoadMethodAttributes (76ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 1.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (7.1 MB). Loaded Objects now: 6652.
Memory consumption went from 143.5 MB to 136.4 MB.
Total: 15.349800 ms (FindLiveObjects: 1.129000 ms CreateObjectMapping: 1.450000 ms MarkObjects: 6.516600 ms  DeleteObjects: 6.252200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.622 seconds
Refreshing native plugins compatible for Editor in 1.18 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.836 seconds
Domain Reload Profiling: 1458ms
	BeginReloadAssembly (200ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (352ms)
		LoadAssemblies (317ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (139ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (122ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (837ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (664ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (110ms)
			ProcessInitializeOnLoadAttributes (462ms)
			ProcessInitializeOnLoadMethodAttributes (72ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 1.15 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (7.2 MB). Loaded Objects now: 6654.
Memory consumption went from 143.5 MB to 136.4 MB.
Total: 15.670700 ms (FindLiveObjects: 1.222500 ms CreateObjectMapping: 1.075300 ms MarkObjects: 6.136000 ms  DeleteObjects: 7.233300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.620 seconds
Refreshing native plugins compatible for Editor in 1.59 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.827 seconds
Domain Reload Profiling: 1449ms
	BeginReloadAssembly (211ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (349ms)
		LoadAssemblies (308ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (135ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (116ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (828ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (658ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (125ms)
			ProcessInitializeOnLoadAttributes (437ms)
			ProcessInitializeOnLoadMethodAttributes (76ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 0.95 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (7.2 MB). Loaded Objects now: 6656.
Memory consumption went from 143.6 MB to 136.4 MB.
Total: 17.868000 ms (FindLiveObjects: 0.940800 ms CreateObjectMapping: 1.340400 ms MarkObjects: 6.965100 ms  DeleteObjects: 8.617500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.627 seconds
Refreshing native plugins compatible for Editor in 1.05 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.691 seconds
Domain Reload Profiling: 1320ms
	BeginReloadAssembly (200ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (365ms)
		LoadAssemblies (321ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (137ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (116ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (691ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (525ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (102ms)
			ProcessInitializeOnLoadAttributes (329ms)
			ProcessInitializeOnLoadMethodAttributes (74ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 1.66 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (7.3 MB). Loaded Objects now: 6658.
Memory consumption went from 143.6 MB to 136.4 MB.
Total: 18.573400 ms (FindLiveObjects: 3.051000 ms CreateObjectMapping: 1.429900 ms MarkObjects: 6.302200 ms  DeleteObjects: 7.786500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.616 seconds
Refreshing native plugins compatible for Editor in 0.55 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.648 seconds
Domain Reload Profiling: 1265ms
	BeginReloadAssembly (187ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (39ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (358ms)
		LoadAssemblies (314ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (140ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (119ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (649ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (511ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (104ms)
			ProcessInitializeOnLoadAttributes (340ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.60 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (6.4 MB). Loaded Objects now: 6660.
Memory consumption went from 143.7 MB to 137.3 MB.
Total: 7.117800 ms (FindLiveObjects: 0.791900 ms CreateObjectMapping: 0.427300 ms MarkObjects: 3.446600 ms  DeleteObjects: 2.451000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.620 seconds
Refreshing native plugins compatible for Editor in 1.56 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.672 seconds
Domain Reload Profiling: 1295ms
	BeginReloadAssembly (202ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (358ms)
		LoadAssemblies (298ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (151ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (130ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (673ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (523ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (105ms)
			ProcessInitializeOnLoadAttributes (323ms)
			ProcessInitializeOnLoadMethodAttributes (75ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 1.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (7.2 MB). Loaded Objects now: 6662.
Memory consumption went from 143.7 MB to 136.5 MB.
Total: 17.295500 ms (FindLiveObjects: 0.947900 ms CreateObjectMapping: 1.440100 ms MarkObjects: 7.756700 ms  DeleteObjects: 7.149100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.755 seconds
Refreshing native plugins compatible for Editor in 0.65 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.666 seconds
Domain Reload Profiling: 1423ms
	BeginReloadAssembly (216ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (458ms)
		LoadAssemblies (349ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (217ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (188ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (666ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (475ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (120ms)
			ProcessInitializeOnLoadAttributes (282ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 0.58 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (6.3 MB). Loaded Objects now: 6664.
Memory consumption went from 143.7 MB to 137.4 MB.
Total: 7.064900 ms (FindLiveObjects: 0.432500 ms CreateObjectMapping: 0.410300 ms MarkObjects: 3.756300 ms  DeleteObjects: 2.464500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.624 seconds
Refreshing native plugins compatible for Editor in 0.55 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.663 seconds
Domain Reload Profiling: 1289ms
	BeginReloadAssembly (195ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (366ms)
		LoadAssemblies (292ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (160ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (140ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (664ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (515ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (114ms)
			ProcessInitializeOnLoadAttributes (312ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 1.09 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (7.3 MB). Loaded Objects now: 6666.
Memory consumption went from 143.8 MB to 136.5 MB.
Total: 18.707700 ms (FindLiveObjects: 1.393700 ms CreateObjectMapping: 0.774000 ms MarkObjects: 8.002500 ms  DeleteObjects: 8.535300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.711 seconds
Refreshing native plugins compatible for Editor in 0.84 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.743 seconds
Domain Reload Profiling: 1455ms
	BeginReloadAssembly (203ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (433ms)
		LoadAssemblies (339ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (193ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (164ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (744ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (557ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (152ms)
			ProcessInitializeOnLoadAttributes (337ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 0.96 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (7.3 MB). Loaded Objects now: 6668.
Memory consumption went from 143.8 MB to 136.5 MB.
Total: 9.112300 ms (FindLiveObjects: 0.600800 ms CreateObjectMapping: 0.510500 ms MarkObjects: 4.216900 ms  DeleteObjects: 3.783100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.677 seconds
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.656 seconds
Domain Reload Profiling: 1334ms
	BeginReloadAssembly (223ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (378ms)
		LoadAssemblies (328ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (152ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (132ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (657ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (507ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (119ms)
			ProcessInitializeOnLoadAttributes (302ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 0.88 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (7.6 MB). Loaded Objects now: 6670.
Memory consumption went from 143.8 MB to 136.3 MB.
Total: 17.786400 ms (FindLiveObjects: 1.056100 ms CreateObjectMapping: 1.513800 ms MarkObjects: 6.767500 ms  DeleteObjects: 8.445500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.602 seconds
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.649 seconds
Domain Reload Profiling: 1252ms
	BeginReloadAssembly (186ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (351ms)
		LoadAssemblies (301ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (138ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (120ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (649ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (498ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (109ms)
			ProcessInitializeOnLoadAttributes (310ms)
			ProcessInitializeOnLoadMethodAttributes (61ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 1.12 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (7.2 MB). Loaded Objects now: 6672.
Memory consumption went from 143.9 MB to 136.7 MB.
Total: 16.233500 ms (FindLiveObjects: 1.191000 ms CreateObjectMapping: 1.238000 ms MarkObjects: 7.265800 ms  DeleteObjects: 6.536400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.638 seconds
Refreshing native plugins compatible for Editor in 0.95 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.731 seconds
Domain Reload Profiling: 1370ms
	BeginReloadAssembly (192ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (366ms)
		LoadAssemblies (310ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (139ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (118ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (732ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (554ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (113ms)
			ProcessInitializeOnLoadAttributes (341ms)
			ProcessInitializeOnLoadMethodAttributes (81ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 1.09 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (6.3 MB). Loaded Objects now: 6674.
Memory consumption went from 143.9 MB to 137.6 MB.
Total: 15.157800 ms (FindLiveObjects: 0.747100 ms CreateObjectMapping: 1.342700 ms MarkObjects: 7.232700 ms  DeleteObjects: 5.832700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.640 seconds
Refreshing native plugins compatible for Editor in 0.77 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.689 seconds
Domain Reload Profiling: 1331ms
	BeginReloadAssembly (196ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (374ms)
		LoadAssemblies (314ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (148ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (123ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (690ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (517ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (102ms)
			ProcessInitializeOnLoadAttributes (323ms)
			ProcessInitializeOnLoadMethodAttributes (73ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 0.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (7.2 MB). Loaded Objects now: 6676.
Memory consumption went from 143.9 MB to 136.7 MB.
Total: 15.836500 ms (FindLiveObjects: 1.221300 ms CreateObjectMapping: 0.842300 ms MarkObjects: 6.167100 ms  DeleteObjects: 7.600000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.622 seconds
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.650 seconds
Domain Reload Profiling: 1275ms
	BeginReloadAssembly (186ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (373ms)
		LoadAssemblies (301ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (156ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (131ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (653ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (500ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (106ms)
			ProcessInitializeOnLoadAttributes (330ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 0.60 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (7.4 MB). Loaded Objects now: 6678.
Memory consumption went from 144.0 MB to 136.6 MB.
Total: 8.556900 ms (FindLiveObjects: 0.451200 ms CreateObjectMapping: 0.386600 ms MarkObjects: 3.783800 ms  DeleteObjects: 3.934100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.603 seconds
Refreshing native plugins compatible for Editor in 0.87 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.674 seconds
Domain Reload Profiling: 1279ms
	BeginReloadAssembly (183ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (353ms)
		LoadAssemblies (290ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (149ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (130ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (674ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (512ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (310ms)
			ProcessInitializeOnLoadMethodAttributes (70ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 1.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (7.2 MB). Loaded Objects now: 6680.
Memory consumption went from 144.0 MB to 136.8 MB.
Total: 16.551100 ms (FindLiveObjects: 0.833000 ms CreateObjectMapping: 1.934600 ms MarkObjects: 6.016700 ms  DeleteObjects: 7.764700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 7718.905494 seconds.
  path: Assets/Scripts/PlayerController.cs
  artifactKey: Guid(521e51cdece08be4a9ba90aa99f267db) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/PlayerController.cs using Guid(521e51cdece08be4a9ba90aa99f267db) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7d55ac2e02e8ff16418ba8161535c892') in 0.0367638 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  3.265 seconds
Refreshing native plugins compatible for Editor in 1.42 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.143 seconds
Domain Reload Profiling: 4413ms
	BeginReloadAssembly (1086ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (102ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (428ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (57ms)
	LoadAllAssembliesAndSetupDomain (2043ms)
		LoadAssemblies (1674ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (570ms)
			TypeCache.Refresh (35ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (501ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (1144ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (842ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (25ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (176ms)
			ProcessInitializeOnLoadAttributes (536ms)
			ProcessInitializeOnLoadMethodAttributes (92ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 1.01 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (7.4 MB). Loaded Objects now: 6682.
Memory consumption went from 144.0 MB to 136.6 MB.
Total: 23.164700 ms (FindLiveObjects: 3.146800 ms CreateObjectMapping: 0.904500 ms MarkObjects: 8.752400 ms  DeleteObjects: 10.355700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.668 seconds
Refreshing native plugins compatible for Editor in 1.17 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.666 seconds
Domain Reload Profiling: 1336ms
	BeginReloadAssembly (196ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (408ms)
		LoadAssemblies (349ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (151ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (132ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (667ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (499ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (305ms)
			ProcessInitializeOnLoadMethodAttributes (70ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 0.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (6.9 MB). Loaded Objects now: 6684.
Memory consumption went from 144.0 MB to 137.2 MB.
Total: 17.333700 ms (FindLiveObjects: 1.026200 ms CreateObjectMapping: 1.314000 ms MarkObjects: 7.938000 ms  DeleteObjects: 7.053300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.620 seconds
Refreshing native plugins compatible for Editor in 0.72 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.641 seconds
Domain Reload Profiling: 1264ms
	BeginReloadAssembly (192ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (355ms)
		LoadAssemblies (299ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (141ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (120ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (642ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (499ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (318ms)
			ProcessInitializeOnLoadMethodAttributes (65ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 1.58 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (7.7 MB). Loaded Objects now: 6686.
Memory consumption went from 144.1 MB to 136.4 MB.
Total: 17.330700 ms (FindLiveObjects: 0.795300 ms CreateObjectMapping: 1.568300 ms MarkObjects: 7.308500 ms  DeleteObjects: 7.655600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.730 seconds
Refreshing native plugins compatible for Editor in 1.01 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.685 seconds
Domain Reload Profiling: 1416ms
	BeginReloadAssembly (221ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (423ms)
		LoadAssemblies (337ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (181ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (159ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (685ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (518ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (114ms)
			ProcessInitializeOnLoadAttributes (312ms)
			ProcessInitializeOnLoadMethodAttributes (70ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 1.38 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (6.9 MB). Loaded Objects now: 6688.
Memory consumption went from 144.1 MB to 137.2 MB.
Total: 17.146100 ms (FindLiveObjects: 1.317300 ms CreateObjectMapping: 1.323800 ms MarkObjects: 7.315600 ms  DeleteObjects: 7.187000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.605 seconds
Refreshing native plugins compatible for Editor in 0.60 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.589 seconds
Domain Reload Profiling: 1194ms
	BeginReloadAssembly (189ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (348ms)
		LoadAssemblies (294ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (145ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (125ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (589ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (460ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (102ms)
			ProcessInitializeOnLoadAttributes (293ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 0.72 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (7.3 MB). Loaded Objects now: 6690.
Memory consumption went from 144.2 MB to 136.8 MB.
Total: 13.278600 ms (FindLiveObjects: 0.516700 ms CreateObjectMapping: 0.538400 ms MarkObjects: 5.315500 ms  DeleteObjects: 6.906500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.623 seconds
Refreshing native plugins compatible for Editor in 1.01 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.726 seconds
Domain Reload Profiling: 1350ms
	BeginReloadAssembly (190ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (363ms)
		LoadAssemblies (306ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (142ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (124ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (727ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (574ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (106ms)
			ProcessInitializeOnLoadAttributes (374ms)
			ProcessInitializeOnLoadMethodAttributes (71ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 1.81 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (7.3 MB). Loaded Objects now: 6692.
Memory consumption went from 144.2 MB to 136.9 MB.
Total: 18.250400 ms (FindLiveObjects: 1.457000 ms CreateObjectMapping: 1.022000 ms MarkObjects: 9.025700 ms  DeleteObjects: 6.743500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.613 seconds
Refreshing native plugins compatible for Editor in 0.57 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.630 seconds
Domain Reload Profiling: 1245ms
	BeginReloadAssembly (198ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (350ms)
		LoadAssemblies (305ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (137ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (118ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (630ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (475ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (115ms)
			ProcessInitializeOnLoadAttributes (302ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 1.26 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (6.6 MB). Loaded Objects now: 6694.
Memory consumption went from 144.2 MB to 137.6 MB.
Total: 14.535800 ms (FindLiveObjects: 1.511200 ms CreateObjectMapping: 0.838500 ms MarkObjects: 6.680300 ms  DeleteObjects: 5.501800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.632 seconds
Refreshing native plugins compatible for Editor in 0.76 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.714 seconds
Domain Reload Profiling: 1347ms
	BeginReloadAssembly (199ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (361ms)
		LoadAssemblies (314ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (141ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (122ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (715ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (551ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (96ms)
			ProcessInitializeOnLoadAttributes (361ms)
			ProcessInitializeOnLoadMethodAttributes (74ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 0.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (6.8 MB). Loaded Objects now: 6696.
Memory consumption went from 144.3 MB to 137.5 MB.
Total: 13.315600 ms (FindLiveObjects: 1.141500 ms CreateObjectMapping: 0.722000 ms MarkObjects: 7.842800 ms  DeleteObjects: 3.605800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.634 seconds
Refreshing native plugins compatible for Editor in 0.60 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.733 seconds
Domain Reload Profiling: 1367ms
	BeginReloadAssembly (195ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (354ms)
		LoadAssemblies (307ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (146ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (127ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (734ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (590ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (113ms)
			ProcessInitializeOnLoadAttributes (375ms)
			ProcessInitializeOnLoadMethodAttributes (79ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 2.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (7.7 MB). Loaded Objects now: 6698.
Memory consumption went from 144.3 MB to 136.6 MB.
Total: 18.816100 ms (FindLiveObjects: 1.286900 ms CreateObjectMapping: 1.102800 ms MarkObjects: 7.411800 ms  DeleteObjects: 9.011900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.623 seconds
Refreshing native plugins compatible for Editor in 0.51 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.579 seconds
Domain Reload Profiling: 1204ms
	BeginReloadAssembly (194ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (367ms)
		LoadAssemblies (311ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (152ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (129ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (579ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (455ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (286ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 1.48 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (7.1 MB). Loaded Objects now: 6700.
Memory consumption went from 144.3 MB to 137.2 MB.
Total: 14.030100 ms (FindLiveObjects: 0.977800 ms CreateObjectMapping: 0.814100 ms MarkObjects: 7.071900 ms  DeleteObjects: 5.163800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.638 seconds
Refreshing native plugins compatible for Editor in 1.32 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.741 seconds
Domain Reload Profiling: 1380ms
	BeginReloadAssembly (195ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (374ms)
		LoadAssemblies (319ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (147ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (128ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (741ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (580ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (118ms)
			ProcessInitializeOnLoadAttributes (365ms)
			ProcessInitializeOnLoadMethodAttributes (75ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 0.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5974 unused Assets / (7.5 MB). Loaded Objects now: 6702.
Memory consumption went from 144.4 MB to 136.8 MB.
Total: 17.470300 ms (FindLiveObjects: 1.152700 ms CreateObjectMapping: 0.772700 ms MarkObjects: 7.692900 ms  DeleteObjects: 7.843900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 0.79 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5968 unused Assets / (7.8 MB). Loaded Objects now: 6703.
Memory consumption went from 144.6 MB to 136.8 MB.
Total: 11.031400 ms (FindLiveObjects: 0.472400 ms CreateObjectMapping: 0.432600 ms MarkObjects: 5.176500 ms  DeleteObjects: 4.948400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.731 seconds
Refreshing native plugins compatible for Editor in 0.95 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.779 seconds
Domain Reload Profiling: 1510ms
	BeginReloadAssembly (218ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (428ms)
		LoadAssemblies (339ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (191ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (161ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (780ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (592ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (125ms)
			ProcessInitializeOnLoadAttributes (381ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 1.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (6.8 MB). Loaded Objects now: 6705.
Memory consumption went from 144.4 MB to 137.6 MB.
Total: 10.892900 ms (FindLiveObjects: 0.544500 ms CreateObjectMapping: 0.856900 ms MarkObjects: 4.873300 ms  DeleteObjects: 4.616600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1632.977881 seconds.
  path: Assets/Scripts/Camera.cs
  artifactKey: Guid(536c08c008ee71541976b805c0abbae8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Camera.cs using Guid(536c08c008ee71541976b805c0abbae8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ed6b74213b08ba52949389b92ecc0baa') in 0.0272545 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 0.81 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5968 unused Assets / (7.2 MB). Loaded Objects now: 6705.
Memory consumption went from 144.6 MB to 137.4 MB.
Total: 11.048700 ms (FindLiveObjects: 0.605600 ms CreateObjectMapping: 1.229300 ms MarkObjects: 5.245200 ms  DeleteObjects: 3.967500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 16.966461 seconds.
  path: Assets/Scripts/CameraController.cs
  artifactKey: Guid(536c08c008ee71541976b805c0abbae8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/CameraController.cs using Guid(536c08c008ee71541976b805c0abbae8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'adce0a4d2d63db0dc9995900e89c5def') in 0.0014048 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.672 seconds
Refreshing native plugins compatible for Editor in 0.55 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.748 seconds
Domain Reload Profiling: 1423ms
	BeginReloadAssembly (208ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (390ms)
		LoadAssemblies (312ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (169ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (141ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (748ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (583ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (128ms)
			ProcessInitializeOnLoadAttributes (377ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 0.80 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (7.4 MB). Loaded Objects now: 6707.
Memory consumption went from 144.5 MB to 137.0 MB.
Total: 12.528000 ms (FindLiveObjects: 0.727500 ms CreateObjectMapping: 0.557900 ms MarkObjects: 5.025300 ms  DeleteObjects: 6.215300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 5.912986 seconds.
  path: Assets/Scripts/CameraController.cs
  artifactKey: Guid(536c08c008ee71541976b805c0abbae8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/CameraController.cs using Guid(536c08c008ee71541976b805c0abbae8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0ed1188883e897b892362f1d2a2d960b') in 0.0047931 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.837 seconds
Refreshing native plugins compatible for Editor in 0.77 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.736 seconds
Domain Reload Profiling: 2576ms
	BeginReloadAssembly (479ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (33ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (133ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (1278ms)
		LoadAssemblies (1196ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (236ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (188ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (737ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (559ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (119ms)
			ProcessInitializeOnLoadAttributes (361ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 0.60 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (7.1 MB). Loaded Objects now: 6709.
Memory consumption went from 144.5 MB to 137.3 MB.
Total: 11.968900 ms (FindLiveObjects: 0.658000 ms CreateObjectMapping: 0.558800 ms MarkObjects: 4.368300 ms  DeleteObjects: 6.382200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.656 seconds
Refreshing native plugins compatible for Editor in 0.57 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.689 seconds
Domain Reload Profiling: 1346ms
	BeginReloadAssembly (197ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (387ms)
		LoadAssemblies (339ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (152ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (128ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (690ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (541ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (113ms)
			ProcessInitializeOnLoadAttributes (340ms)
			ProcessInitializeOnLoadMethodAttributes (68ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (8.0 MB). Loaded Objects now: 6711.
Memory consumption went from 144.5 MB to 136.5 MB.
Total: 17.686200 ms (FindLiveObjects: 0.906700 ms CreateObjectMapping: 1.235300 ms MarkObjects: 6.490000 ms  DeleteObjects: 9.052000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 3853.116719 seconds.
  path: Assets/Scripts/CameraController.cs
  artifactKey: Guid(536c08c008ee71541976b805c0abbae8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/CameraController.cs using Guid(536c08c008ee71541976b805c0abbae8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '04db81407e325dbe68ed816e4e5c7d6e') in 0.0096912 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.895 seconds
Refreshing native plugins compatible for Editor in 0.84 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.932 seconds
Domain Reload Profiling: 1826ms
	BeginReloadAssembly (273ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (527ms)
		LoadAssemblies (414ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (227ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (201ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (932ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (744ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (119ms)
			ProcessInitializeOnLoadAttributes (522ms)
			ProcessInitializeOnLoadMethodAttributes (82ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 1.49 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (7.7 MB). Loaded Objects now: 6713.
Memory consumption went from 144.6 MB to 136.9 MB.
Total: 23.004600 ms (FindLiveObjects: 2.758100 ms CreateObjectMapping: 1.142400 ms MarkObjects: 9.517800 ms  DeleteObjects: 9.579800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.631 seconds
Refreshing native plugins compatible for Editor in 0.71 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.690 seconds
Domain Reload Profiling: 1324ms
	BeginReloadAssembly (198ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (367ms)
		LoadAssemblies (317ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (144ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (125ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (691ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (532ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (110ms)
			ProcessInitializeOnLoadAttributes (322ms)
			ProcessInitializeOnLoadMethodAttributes (76ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 1.54 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (7.3 MB). Loaded Objects now: 6715.
Memory consumption went from 144.6 MB to 137.3 MB.
Total: 17.194900 ms (FindLiveObjects: 1.206200 ms CreateObjectMapping: 0.841700 ms MarkObjects: 6.502600 ms  DeleteObjects: 8.641500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.599 seconds
Refreshing native plugins compatible for Editor in 0.67 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.598 seconds
Domain Reload Profiling: 1198ms
	BeginReloadAssembly (184ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (349ms)
		LoadAssemblies (297ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (137ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (120ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (599ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (466ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (294ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 1.26 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (7.3 MB). Loaded Objects now: 6717.
Memory consumption went from 144.6 MB to 137.3 MB.
Total: 18.827200 ms (FindLiveObjects: 1.613400 ms CreateObjectMapping: 1.122500 ms MarkObjects: 7.547500 ms  DeleteObjects: 8.540500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.626 seconds
Refreshing native plugins compatible for Editor in 0.56 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.752 seconds
Domain Reload Profiling: 1380ms
	BeginReloadAssembly (190ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (375ms)
		LoadAssemblies (320ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (148ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (130ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (753ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (598ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (401ms)
			ProcessInitializeOnLoadMethodAttributes (77ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 1.83 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (7.7 MB). Loaded Objects now: 6719.
Memory consumption went from 144.7 MB to 137.0 MB.
Total: 19.192700 ms (FindLiveObjects: 1.450300 ms CreateObjectMapping: 1.173100 ms MarkObjects: 7.643900 ms  DeleteObjects: 8.922700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.801 seconds
Refreshing native plugins compatible for Editor in 0.96 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.021 seconds
Domain Reload Profiling: 1822ms
	BeginReloadAssembly (257ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (473ms)
		LoadAssemblies (404ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (169ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (148ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1022ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (831ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (163ms)
			ProcessInitializeOnLoadAttributes (556ms)
			ProcessInitializeOnLoadMethodAttributes (83ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 2.14 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (7.1 MB). Loaded Objects now: 6721.
Memory consumption went from 144.7 MB to 137.5 MB.
Total: 23.204700 ms (FindLiveObjects: 2.934500 ms CreateObjectMapping: 1.129000 ms MarkObjects: 10.680700 ms  DeleteObjects: 8.456000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.619 seconds
Refreshing native plugins compatible for Editor in 0.54 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.651 seconds
Domain Reload Profiling: 1272ms
	BeginReloadAssembly (193ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (357ms)
		LoadAssemblies (310ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (137ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (118ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (652ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (496ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (111ms)
			ProcessInitializeOnLoadAttributes (302ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 1.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5975 unused Assets / (8.5 MB). Loaded Objects now: 6723.
Memory consumption went from 144.7 MB to 136.2 MB.
Total: 33.775600 ms (FindLiveObjects: 1.153200 ms CreateObjectMapping: 1.387900 ms MarkObjects: 5.573900 ms  DeleteObjects: 25.657700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 3962.275814 seconds.
  path: Assets/Materials/Player 1.mat
  artifactKey: Guid(bb28775b4bdbab14d829053c0f60de5b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Player 1.mat using Guid(bb28775b4bdbab14d829053c0f60de5b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7df67782d82ae0ed267c399b8dc90a35') in 0.7399077 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 12.259861 seconds.
  path: Assets/Materials/Pick-up.mat
  artifactKey: Guid(bb28775b4bdbab14d829053c0f60de5b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/Pick-up.mat using Guid(bb28775b4bdbab14d829053c0f60de5b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6710005f1862ea80de0b4d206dcfb287') in 0.0227171 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.03 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5970 unused Assets / (7.4 MB). Loaded Objects now: 6908.
Memory consumption went from 155.1 MB to 147.7 MB.
Total: 38.079400 ms (FindLiveObjects: 0.947900 ms CreateObjectMapping: 0.790500 ms MarkObjects: 31.079300 ms  DeleteObjects: 5.258800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.924 seconds
Refreshing native plugins compatible for Editor in 0.66 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.735 seconds
Domain Reload Profiling: 1660ms
	BeginReloadAssembly (337ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (26ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (118ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (508ms)
		LoadAssemblies (477ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (166ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (138ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (736ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (562ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (130ms)
			ProcessInitializeOnLoadAttributes (360ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 0.77 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5976 unused Assets / (7.2 MB). Loaded Objects now: 6789.
Memory consumption went from 150.1 MB to 142.9 MB.
Total: 9.007700 ms (FindLiveObjects: 1.039500 ms CreateObjectMapping: 0.452800 ms MarkObjects: 3.573900 ms  DeleteObjects: 3.940200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 0.93 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5969 unused Assets / (6.8 MB). Loaded Objects now: 6789.
Memory consumption went from 150.1 MB to 143.3 MB.
Total: 9.417100 ms (FindLiveObjects: 0.630800 ms CreateObjectMapping: 0.479400 ms MarkObjects: 4.322500 ms  DeleteObjects: 3.983800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.668 seconds
Refreshing native plugins compatible for Editor in 0.59 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.770 seconds
Domain Reload Profiling: 1439ms
	BeginReloadAssembly (192ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (404ms)
		LoadAssemblies (338ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (157ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (134ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (771ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (607ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (113ms)
			ProcessInitializeOnLoadAttributes (420ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.85 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5976 unused Assets / (6.3 MB). Loaded Objects now: 6791.
Memory consumption went from 150.0 MB to 143.7 MB.
Total: 11.943700 ms (FindLiveObjects: 0.827800 ms CreateObjectMapping: 0.657600 ms MarkObjects: 5.009800 ms  DeleteObjects: 5.447100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 804.389136 seconds.
  path: Assets/Scripts/rotator.cs
  artifactKey: Guid(2344d73b2f96c4c468fbe6ad2b8641b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/rotator.cs using Guid(2344d73b2f96c4c468fbe6ad2b8641b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3f9d33530fdd6dea11ddc55666729336') in 0.0069095 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  3.549 seconds
Refreshing native plugins compatible for Editor in 2.26 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.916 seconds
Domain Reload Profiling: 4467ms
	BeginReloadAssembly (1331ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (127ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (634ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (72ms)
	LoadAllAssembliesAndSetupDomain (2071ms)
		LoadAssemblies (2034ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (237ms)
			TypeCache.Refresh (49ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (171ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (917ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (723ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (109ms)
			ProcessInitializeOnLoadAttributes (495ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (27ms)
Refreshing native plugins compatible for Editor in 0.84 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5979 unused Assets / (7.0 MB). Loaded Objects now: 6796.
Memory consumption went from 150.1 MB to 143.1 MB.
Total: 14.047200 ms (FindLiveObjects: 0.962000 ms CreateObjectMapping: 0.619200 ms MarkObjects: 7.990300 ms  DeleteObjects: 4.473700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.228 seconds
Refreshing native plugins compatible for Editor in 3.09 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.212 seconds
Domain Reload Profiling: 2444ms
	BeginReloadAssembly (321ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (95ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (837ms)
		LoadAssemblies (690ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (293ms)
			TypeCache.Refresh (34ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (237ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1213ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (871ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (174ms)
			ProcessInitializeOnLoadAttributes (573ms)
			ProcessInitializeOnLoadMethodAttributes (91ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 2.85 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5980 unused Assets / (7.2 MB). Loaded Objects now: 6799.
Memory consumption went from 150.1 MB to 142.8 MB.
Total: 25.521500 ms (FindLiveObjects: 2.626200 ms CreateObjectMapping: 1.420900 ms MarkObjects: 11.677000 ms  DeleteObjects: 9.792800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.709 seconds
Refreshing native plugins compatible for Editor in 0.84 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.797 seconds
Domain Reload Profiling: 1507ms
	BeginReloadAssembly (239ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (391ms)
		LoadAssemblies (323ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (172ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (151ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (798ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (633ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (119ms)
			ProcessInitializeOnLoadAttributes (408ms)
			ProcessInitializeOnLoadMethodAttributes (85ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 2.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5980 unused Assets / (7.1 MB). Loaded Objects now: 6801.
Memory consumption went from 150.1 MB to 143.0 MB.
Total: 19.108300 ms (FindLiveObjects: 1.163500 ms CreateObjectMapping: 2.190900 ms MarkObjects: 7.692400 ms  DeleteObjects: 8.059100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.810 seconds
Refreshing native plugins compatible for Editor in 1.21 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.785 seconds
Domain Reload Profiling: 1597ms
	BeginReloadAssembly (294ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (99ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (445ms)
		LoadAssemblies (385ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (163ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (137ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (786ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (567ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (150ms)
			ProcessInitializeOnLoadAttributes (347ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 0.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5980 unused Assets / (6.9 MB). Loaded Objects now: 6803.
Memory consumption went from 150.2 MB to 143.3 MB.
Total: 9.895700 ms (FindLiveObjects: 0.949200 ms CreateObjectMapping: 0.543400 ms MarkObjects: 4.456900 ms  DeleteObjects: 3.944700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.628 seconds
Refreshing native plugins compatible for Editor in 1.27 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.786 seconds
Domain Reload Profiling: 1415ms
	BeginReloadAssembly (192ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (370ms)
		LoadAssemblies (305ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (151ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (129ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (787ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (609ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (106ms)
			ProcessInitializeOnLoadAttributes (403ms)
			ProcessInitializeOnLoadMethodAttributes (80ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (26ms)
Refreshing native plugins compatible for Editor in 1.84 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5980 unused Assets / (7.9 MB). Loaded Objects now: 6805.
Memory consumption went from 150.2 MB to 142.3 MB.
Total: 19.343400 ms (FindLiveObjects: 1.082100 ms CreateObjectMapping: 1.200200 ms MarkObjects: 8.060900 ms  DeleteObjects: 8.994000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.671 seconds
Refreshing native plugins compatible for Editor in 1.17 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.831 seconds
Domain Reload Profiling: 1505ms
	BeginReloadAssembly (212ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (386ms)
		LoadAssemblies (324ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (165ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (146ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (832ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (648ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (434ms)
			ProcessInitializeOnLoadMethodAttributes (80ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (24ms)
Refreshing native plugins compatible for Editor in 2.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5980 unused Assets / (8.6 MB). Loaded Objects now: 6807.
Memory consumption went from 150.2 MB to 141.6 MB.
Total: 18.883900 ms (FindLiveObjects: 0.963900 ms CreateObjectMapping: 2.110500 ms MarkObjects: 6.806000 ms  DeleteObjects: 8.999800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.660 seconds
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.686 seconds
Domain Reload Profiling: 1347ms
	BeginReloadAssembly (211ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (388ms)
		LoadAssemblies (332ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (153ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (130ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (687ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (520ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (112ms)
			ProcessInitializeOnLoadAttributes (311ms)
			ProcessInitializeOnLoadMethodAttributes (74ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 2.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 31 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5980 unused Assets / (6.6 MB). Loaded Objects now: 6809.
Memory consumption went from 150.2 MB to 143.7 MB.
Total: 12.944800 ms (FindLiveObjects: 1.031800 ms CreateObjectMapping: 1.437300 ms MarkObjects: 6.031700 ms  DeleteObjects: 4.442400 ms)

Prepare: number of updated asset objects reloaded= 0
