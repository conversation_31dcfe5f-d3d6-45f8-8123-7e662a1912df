{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 6268, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 6268, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 6268, "tid": 3734, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 6268, "tid": 3734, "ts": 1751900209268388, "dur": 1066, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 6268, "tid": 3734, "ts": 1751900209275918, "dur": 827, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 6268, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 6268, "tid": 1, "ts": 1751900208482883, "dur": 6151, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 6268, "tid": 1, "ts": 1751900208489038, "dur": 59101, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 6268, "tid": 1, "ts": 1751900208548148, "dur": 116261, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 6268, "tid": 3734, "ts": 1751900209276750, "dur": 14, "ph": "X", "name": "", "args": {}}, {"pid": 6268, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208480934, "dur": 11969, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208492906, "dur": 766212, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208494047, "dur": 2643, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208496697, "dur": 1663, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208498364, "dur": 219, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208498587, "dur": 12, "ph": "X", "name": "ProcessMessages 20501", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208498601, "dur": 447, "ph": "X", "name": "ReadAsync 20501", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499051, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499054, "dur": 119, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499176, "dur": 5, "ph": "X", "name": "ProcessMessages 8093", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499182, "dur": 36, "ph": "X", "name": "ReadAsync 8093", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499220, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499222, "dur": 43, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499269, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499272, "dur": 37, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499311, "dur": 1, "ph": "X", "name": "ProcessMessages 917", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499313, "dur": 27, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499342, "dur": 18, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499362, "dur": 2, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499364, "dur": 33, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499400, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499421, "dur": 22, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499446, "dur": 19, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499467, "dur": 26, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499496, "dur": 23, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499522, "dur": 22, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499547, "dur": 23, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499573, "dur": 23, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499599, "dur": 27, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499629, "dur": 22, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499653, "dur": 24, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499680, "dur": 20, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499703, "dur": 20, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499725, "dur": 20, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499748, "dur": 19, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499769, "dur": 20, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499792, "dur": 47, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499841, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499880, "dur": 22, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499905, "dur": 22, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499930, "dur": 27, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499960, "dur": 20, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208499982, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500004, "dur": 23, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500030, "dur": 19, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500052, "dur": 19, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500073, "dur": 23, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500099, "dur": 18, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500120, "dur": 20, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500142, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500143, "dur": 34, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500181, "dur": 23, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500207, "dur": 20, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500230, "dur": 21, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500253, "dur": 23, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500279, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500300, "dur": 20, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500324, "dur": 22, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500349, "dur": 18, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500370, "dur": 18, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500391, "dur": 19, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500412, "dur": 17, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500432, "dur": 44, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500479, "dur": 20, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500502, "dur": 28, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500533, "dur": 24, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500560, "dur": 29, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500591, "dur": 22, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500616, "dur": 24, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500642, "dur": 31, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500677, "dur": 22, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500702, "dur": 21, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500726, "dur": 22, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500751, "dur": 22, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500775, "dur": 24, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500801, "dur": 24, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500828, "dur": 21, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500852, "dur": 25, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500880, "dur": 21, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500904, "dur": 23, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500929, "dur": 23, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500955, "dur": 25, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208500983, "dur": 38, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501023, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501049, "dur": 22, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501073, "dur": 36, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501112, "dur": 26, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501140, "dur": 22, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501165, "dur": 26, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501195, "dur": 253, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501451, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501492, "dur": 1, "ph": "X", "name": "ProcessMessages 921", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501494, "dur": 46, "ph": "X", "name": "ReadAsync 921", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501542, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501545, "dur": 30, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501576, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501578, "dur": 35, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501615, "dur": 1, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501616, "dur": 32, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501650, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501652, "dur": 38, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501692, "dur": 1, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501694, "dur": 25, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501726, "dur": 34, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501761, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501763, "dur": 32, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501797, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501799, "dur": 28, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501829, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501830, "dur": 28, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501861, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501862, "dur": 29, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501894, "dur": 33, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501929, "dur": 1, "ph": "X", "name": "ProcessMessages 975", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501931, "dur": 32, "ph": "X", "name": "ReadAsync 975", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501965, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501967, "dur": 22, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501991, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208501992, "dur": 31, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502026, "dur": 1, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502027, "dur": 28, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502058, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502059, "dur": 32, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502093, "dur": 1, "ph": "X", "name": "ProcessMessages 947", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502095, "dur": 33, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502132, "dur": 39, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502175, "dur": 34, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502213, "dur": 33, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502249, "dur": 23, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502275, "dur": 21, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502299, "dur": 26, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502328, "dur": 23, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502353, "dur": 23, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502379, "dur": 22, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502404, "dur": 22, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502429, "dur": 28, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502460, "dur": 27, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502489, "dur": 22, "ph": "X", "name": "ReadAsync 981", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502514, "dur": 33, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502550, "dur": 43, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502595, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502598, "dur": 35, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502634, "dur": 1, "ph": "X", "name": "ProcessMessages 907", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502636, "dur": 29, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502668, "dur": 1, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502670, "dur": 33, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502706, "dur": 30, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502737, "dur": 1, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502739, "dur": 26, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502767, "dur": 23, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502794, "dur": 26, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502823, "dur": 30, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502855, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502857, "dur": 31, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502890, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502892, "dur": 24, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502918, "dur": 34, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502953, "dur": 1, "ph": "X", "name": "ProcessMessages 996", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502955, "dur": 34, "ph": "X", "name": "ReadAsync 996", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502991, "dur": 1, "ph": "X", "name": "ProcessMessages 1006", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208502993, "dur": 21, "ph": "X", "name": "ReadAsync 1006", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503017, "dur": 27, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503046, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503048, "dur": 29, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503079, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503081, "dur": 39, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503122, "dur": 2, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503125, "dur": 30, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503157, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503158, "dur": 31, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503192, "dur": 28, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503222, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503223, "dur": 27, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503252, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503254, "dur": 26, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503283, "dur": 21, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503307, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503332, "dur": 38, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503372, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503374, "dur": 25, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503401, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503402, "dur": 25, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503429, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503430, "dur": 24, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503457, "dur": 28, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503487, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503489, "dur": 26, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503517, "dur": 25, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503544, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503546, "dur": 22, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503571, "dur": 26, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503598, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503600, "dur": 26, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503629, "dur": 26, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503658, "dur": 27, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503689, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503692, "dur": 50, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503744, "dur": 1, "ph": "X", "name": "ProcessMessages 1100", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503746, "dur": 38, "ph": "X", "name": "ReadAsync 1100", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503787, "dur": 1, "ph": "X", "name": "ProcessMessages 933", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503790, "dur": 38, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503830, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503832, "dur": 29, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503864, "dur": 31, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503897, "dur": 1, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503899, "dur": 27, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503929, "dur": 24, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503957, "dur": 32, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208503992, "dur": 28, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504023, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504025, "dur": 27, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504053, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504055, "dur": 25, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504082, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504084, "dur": 24, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504111, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504113, "dur": 26, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504142, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504144, "dur": 32, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504178, "dur": 1, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504179, "dur": 24, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504205, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504208, "dur": 28, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504239, "dur": 29, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504270, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504272, "dur": 28, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504302, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504305, "dur": 26, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504333, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504335, "dur": 28, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504365, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504367, "dur": 28, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504399, "dur": 26, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504427, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504428, "dur": 27, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504457, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504458, "dur": 22, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504483, "dur": 28, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504514, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504516, "dur": 32, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504550, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504551, "dur": 25, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504579, "dur": 1, "ph": "X", "name": "ProcessMessages 133", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504580, "dur": 26, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504609, "dur": 26, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504637, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504638, "dur": 42, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504683, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504684, "dur": 23, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504709, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504710, "dur": 28, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504742, "dur": 21, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504766, "dur": 28, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504920, "dur": 51, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504976, "dur": 3, "ph": "X", "name": "ProcessMessages 4303", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208504980, "dur": 26, "ph": "X", "name": "ReadAsync 4303", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505008, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505010, "dur": 27, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505040, "dur": 27, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505070, "dur": 23, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505097, "dur": 24, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505124, "dur": 27, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505153, "dur": 1, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505154, "dur": 26, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505182, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505183, "dur": 28, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505213, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505214, "dur": 25, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505242, "dur": 24, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505271, "dur": 23, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505297, "dur": 29, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505330, "dur": 1, "ph": "X", "name": "ProcessMessages 229", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505333, "dur": 41, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505376, "dur": 1, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505378, "dur": 38, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505418, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505420, "dur": 29, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505451, "dur": 1, "ph": "X", "name": "ProcessMessages 935", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505452, "dur": 25, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505481, "dur": 24, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505507, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505509, "dur": 22, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505534, "dur": 23, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505560, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505561, "dur": 107, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505672, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505712, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505714, "dur": 27, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505742, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505745, "dur": 23, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505773, "dur": 31, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505806, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505808, "dur": 27, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505837, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505838, "dur": 32, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505873, "dur": 21, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505897, "dur": 22, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505921, "dur": 54, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505978, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208505980, "dur": 43, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506025, "dur": 1, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506027, "dur": 23, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506052, "dur": 26, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506082, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506120, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506122, "dur": 27, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506151, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506153, "dur": 36, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506192, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506194, "dur": 27, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506224, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506226, "dur": 32, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506261, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506262, "dur": 24, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506288, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506290, "dur": 40, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506332, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506334, "dur": 27, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506363, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506364, "dur": 26, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506392, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506394, "dur": 28, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506425, "dur": 24, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506452, "dur": 22, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506476, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506477, "dur": 22, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506502, "dur": 43, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506553, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506555, "dur": 40, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506599, "dur": 1, "ph": "X", "name": "ProcessMessages 1191", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506602, "dur": 30, "ph": "X", "name": "ReadAsync 1191", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506637, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506639, "dur": 31, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506673, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506675, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506724, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506770, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506773, "dur": 28, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506805, "dur": 100, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506909, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506938, "dur": 27, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506967, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208506969, "dur": 31, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507003, "dur": 72, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507079, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507118, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507120, "dur": 34, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507158, "dur": 64, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507225, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507261, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507262, "dur": 71, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507336, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507337, "dur": 24, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507363, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507392, "dur": 24, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507419, "dur": 74, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507501, "dur": 148, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507652, "dur": 1, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507657, "dur": 45, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507704, "dur": 1, "ph": "X", "name": "ProcessMessages 1305", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507715, "dur": 27, "ph": "X", "name": "ReadAsync 1305", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507744, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507780, "dur": 1, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507781, "dur": 32, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507815, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507818, "dur": 52, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507879, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507924, "dur": 1, "ph": "X", "name": "ProcessMessages 1045", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507926, "dur": 21, "ph": "X", "name": "ReadAsync 1045", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208507955, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508022, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508055, "dur": 1, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508056, "dur": 23, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508082, "dur": 4, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508088, "dur": 21, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508113, "dur": 59, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508180, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508223, "dur": 22, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508249, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508251, "dur": 102, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508363, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508400, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508403, "dur": 29, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508435, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508436, "dur": 49, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508490, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508532, "dur": 6, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508539, "dur": 24, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508567, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508569, "dur": 59, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508630, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508632, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508666, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508669, "dur": 23, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508695, "dur": 206, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508911, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508957, "dur": 2, "ph": "X", "name": "ProcessMessages 1532", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508961, "dur": 28, "ph": "X", "name": "ReadAsync 1532", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508996, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208508997, "dur": 52, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509053, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509077, "dur": 1, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509078, "dur": 39, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509126, "dur": 53, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509183, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509213, "dur": 1, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509214, "dur": 45, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509263, "dur": 31, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509308, "dur": 30, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509342, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509400, "dur": 2, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509403, "dur": 30, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509438, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509440, "dur": 40, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509487, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509522, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509524, "dur": 27, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509553, "dur": 1, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509559, "dur": 28, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509588, "dur": 7, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509597, "dur": 27, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509627, "dur": 32, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509661, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509663, "dur": 36, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509707, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509740, "dur": 5, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509745, "dur": 30, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509779, "dur": 39, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509819, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509822, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509856, "dur": 1, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509858, "dur": 17, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509882, "dur": 37, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509923, "dur": 5, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509930, "dur": 24, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509961, "dur": 1, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509963, "dur": 22, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208509989, "dur": 58, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510054, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510090, "dur": 1, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510091, "dur": 31, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510125, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510130, "dur": 60, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510193, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510232, "dur": 1, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510234, "dur": 29, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510267, "dur": 31, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510303, "dur": 39, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510345, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510347, "dur": 26, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510375, "dur": 58, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510439, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510462, "dur": 30, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510502, "dur": 23, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510531, "dur": 56, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510592, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510634, "dur": 4, "ph": "X", "name": "ProcessMessages 1117", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510639, "dur": 45, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510687, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510715, "dur": 1, "ph": "X", "name": "ProcessMessages 820", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510717, "dur": 53, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510775, "dur": 2, "ph": "X", "name": "ProcessMessages 1066", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510778, "dur": 35, "ph": "X", "name": "ReadAsync 1066", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510817, "dur": 35, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510860, "dur": 32, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510895, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510953, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510993, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208510995, "dur": 33, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511055, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511056, "dur": 29, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511098, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511137, "dur": 1, "ph": "X", "name": "ProcessMessages 1025", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511140, "dur": 29, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511171, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511172, "dur": 48, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511224, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511262, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511267, "dur": 23, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511297, "dur": 56, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511357, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511393, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511396, "dur": 33, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511440, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511442, "dur": 21, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511465, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511466, "dur": 46, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511519, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511552, "dur": 1, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511562, "dur": 26, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511589, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511591, "dur": 42, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511636, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511682, "dur": 30, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511715, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511716, "dur": 202, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511930, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511978, "dur": 2, "ph": "X", "name": "ProcessMessages 2289", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208511982, "dur": 31, "ph": "X", "name": "ReadAsync 2289", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512015, "dur": 1, "ph": "X", "name": "ProcessMessages 1193", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512017, "dur": 30, "ph": "X", "name": "ReadAsync 1193", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512048, "dur": 7, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512057, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512111, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512151, "dur": 1, "ph": "X", "name": "ProcessMessages 1237", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512157, "dur": 28, "ph": "X", "name": "ReadAsync 1237", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512196, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512237, "dur": 1, "ph": "X", "name": "ProcessMessages 1216", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512239, "dur": 53, "ph": "X", "name": "ReadAsync 1216", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512297, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512333, "dur": 1, "ph": "X", "name": "ProcessMessages 1012", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512334, "dur": 23, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512361, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512419, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512461, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512462, "dur": 22, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512487, "dur": 6, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512495, "dur": 62, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512561, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512609, "dur": 5, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512616, "dur": 28, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512653, "dur": 2, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512656, "dur": 29, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512690, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512723, "dur": 1, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512725, "dur": 193, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512921, "dur": 37, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512966, "dur": 2, "ph": "X", "name": "ProcessMessages 2350", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512970, "dur": 14, "ph": "X", "name": "ReadAsync 2350", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208512995, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513013, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513015, "dur": 25, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513043, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513045, "dur": 45, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513094, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513095, "dur": 39, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513142, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513144, "dur": 31, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513177, "dur": 2, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513180, "dur": 39, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513225, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513256, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513262, "dur": 25, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513292, "dur": 61, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513356, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513413, "dur": 1, "ph": "X", "name": "ProcessMessages 1219", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513417, "dur": 34, "ph": "X", "name": "ReadAsync 1219", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513453, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513455, "dur": 22, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513479, "dur": 10, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513490, "dur": 30, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513523, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513525, "dur": 19, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513554, "dur": 53, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513609, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513611, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513655, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513657, "dur": 33, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513693, "dur": 45, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513741, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513927, "dur": 7, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513935, "dur": 37, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513975, "dur": 2, "ph": "X", "name": "ProcessMessages 2858", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208513980, "dur": 29, "ph": "X", "name": "ReadAsync 2858", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208514015, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208514017, "dur": 28, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208514051, "dur": 42, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208514094, "dur": 1, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208514100, "dur": 33, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208514141, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208514143, "dur": 168, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208514314, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208514316, "dur": 49, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208514369, "dur": 2, "ph": "X", "name": "ProcessMessages 2764", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208514373, "dur": 285, "ph": "X", "name": "ReadAsync 2764", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208514672, "dur": 4, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208514679, "dur": 56, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208514744, "dur": 368, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208515117, "dur": 1009, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208516133, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208516135, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208516217, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208516221, "dur": 226, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208516457, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208516461, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208516521, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208516524, "dur": 52, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208516587, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208516592, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208516642, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208516693, "dur": 170, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208516873, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208516876, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208516926, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208516929, "dur": 118, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517058, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517061, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517102, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517104, "dur": 62, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517169, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517177, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517235, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517318, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517360, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517363, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517483, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517487, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517525, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517571, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517574, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517651, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517678, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517709, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517795, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517798, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517836, "dur": 10, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517847, "dur": 65, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208517934, "dur": 139, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208518077, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208518080, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208518126, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208518129, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208518184, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208518186, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208518224, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208518226, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208518292, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208518354, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208518358, "dur": 184, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208518738, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208518743, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208518794, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208518797, "dur": 30, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208518832, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208518883, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208518885, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208518958, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208518961, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208519003, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208519049, "dur": 295, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208519350, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208519415, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208519418, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208519457, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208519460, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208519505, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208519507, "dur": 31, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208519541, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208519543, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208519571, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208519573, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208519608, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208519611, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208519663, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208519969, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208519972, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520015, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520018, "dur": 35, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520063, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520065, "dur": 207, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520278, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520311, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520314, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520348, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520350, "dur": 147, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520502, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520540, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520543, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520574, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520576, "dur": 124, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520704, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520706, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520770, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520772, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520810, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520813, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520852, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520855, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208520889, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521111, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521115, "dur": 78, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521198, "dur": 3, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521202, "dur": 20, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521223, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521225, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521267, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521269, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521302, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521305, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521332, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521334, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521385, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521421, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521425, "dur": 37, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521464, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521476, "dur": 31, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521509, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521512, "dur": 38, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521554, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521576, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521579, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521609, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521612, "dur": 41, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521656, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521659, "dur": 33, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521694, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521702, "dur": 29, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521733, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521736, "dur": 37, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521777, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521786, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521819, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521822, "dur": 41, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521868, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521870, "dur": 45, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521922, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521925, "dur": 25, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521952, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521954, "dur": 23, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521979, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208521981, "dur": 23, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522007, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522009, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522038, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522041, "dur": 20, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522063, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522113, "dur": 8, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522123, "dur": 47, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522172, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522175, "dur": 29, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522206, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522209, "dur": 31, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522244, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522247, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522279, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522281, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522312, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522314, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522344, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522348, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522391, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522393, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522420, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522423, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522454, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522456, "dur": 37, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522496, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522498, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522544, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522546, "dur": 28, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522589, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522592, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522623, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522632, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522665, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522672, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522697, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522699, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522736, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522738, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522774, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522777, "dur": 31, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522810, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522813, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522842, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522846, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522871, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522874, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522901, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522904, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522932, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522935, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522963, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522965, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522996, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208522998, "dur": 36808, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208559812, "dur": 9, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208559823, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208559864, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208559866, "dur": 200, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208560070, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208560072, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208560124, "dur": 3178, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208563307, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208563310, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208563352, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208563354, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208563391, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208563394, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208563435, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208563437, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208563480, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208563482, "dur": 180, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208563667, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208563711, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208563713, "dur": 23757, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208587475, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208587478, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208587541, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208587544, "dur": 341, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208587893, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208587935, "dur": 120, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208588058, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208588087, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208588199, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208588226, "dur": 442, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208588672, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208588676, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208588714, "dur": 975, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208589695, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208589734, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208589736, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208589759, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208589805, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208589836, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208589838, "dur": 191, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208590033, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208590060, "dur": 101, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208590605, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208590607, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208590650, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208590653, "dur": 31, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208590687, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208590744, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208590779, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208590805, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208590837, "dur": 170, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591011, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591040, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591042, "dur": 54, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591102, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591140, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591162, "dur": 268, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591436, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591467, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591524, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591550, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591577, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591618, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591643, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591645, "dur": 118, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591767, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591814, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591839, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591862, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591885, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591944, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591980, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208591984, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592013, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592043, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592045, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592114, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592145, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592146, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592176, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592178, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592237, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592266, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592292, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592328, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592330, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592425, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592456, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592457, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592481, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592503, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592526, "dur": 125, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592654, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592679, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592718, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592741, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592765, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592792, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592814, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592837, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592872, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592917, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592919, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592958, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592960, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592991, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208592993, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593020, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593095, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593120, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593207, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593209, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593241, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593267, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593298, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593348, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593374, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593399, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593451, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593478, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593480, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593510, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593599, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593628, "dur": 116, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593749, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593781, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593783, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593815, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593856, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593858, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593909, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593937, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208593972, "dur": 341, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208594317, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208594355, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208594385, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208594416, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208594450, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208594484, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208594510, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208594608, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208594634, "dur": 125, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208594762, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208594763, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208594803, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208594804, "dur": 122, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208594930, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208594964, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208594994, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208594996, "dur": 57, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208595056, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208595086, "dur": 207, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208595297, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208595299, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208595327, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208595328, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208595361, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208595395, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208595479, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208595480, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208595506, "dur": 1050, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208596564, "dur": 68, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208596637, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208596641, "dur": 27, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208596671, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208596711, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208596714, "dur": 157, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208596876, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208596878, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208596914, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208596916, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208597001, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208597029, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208597031, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208597141, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208597169, "dur": 179, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208597353, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208597387, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208597389, "dur": 226, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208597618, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208597620, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208597657, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208597686, "dur": 335, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208598023, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208598029, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208598059, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208598083, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208598197, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208598238, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208598240, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208598275, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208598277, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208598309, "dur": 278, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208598590, "dur": 15, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208598606, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208598667, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208598669, "dur": 132, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208598806, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208598837, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208598839, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208598868, "dur": 213, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208599085, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208599121, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208599123, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208599227, "dur": 481, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208599724, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208599726, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208599752, "dur": 442, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208600362, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208600419, "dur": 75, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208600506, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208600507, "dur": 266, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208600781, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208600813, "dur": 517, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208601336, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208601409, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208601413, "dur": 71839, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208673257, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208673260, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208673297, "dur": 1442, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208674743, "dur": 12299, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208687051, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208687055, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208687118, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208687120, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208687153, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208687156, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208687184, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208687216, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208687272, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208687300, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208687302, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208687342, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208687368, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208687372, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208687414, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208687437, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208687448, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208687514, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208687543, "dur": 1720, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689267, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689269, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689303, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689305, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689334, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689336, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689371, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689394, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689396, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689456, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689457, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689489, "dur": 69, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689561, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689565, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689593, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689595, "dur": 181, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689782, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689839, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689842, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689883, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689885, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689977, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208689979, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208690022, "dur": 1501, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208691528, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208691569, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208691571, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208691621, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208691665, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208691696, "dur": 570, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208692269, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208692271, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208692306, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208692310, "dur": 247, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208692725, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208692728, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208692763, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208692764, "dur": 523, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208693291, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208693293, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208693327, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208693354, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208693356, "dur": 554, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208693914, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208693942, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208693944, "dur": 212, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208694160, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208694199, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208694201, "dur": 346, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208694552, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208694577, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208694622, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208694650, "dur": 209, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208694863, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208694890, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208694892, "dur": 85, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208694981, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208695018, "dur": 499, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208695521, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208695523, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208695557, "dur": 290, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208695853, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208695885, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208695887, "dur": 171, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696062, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696089, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696091, "dur": 297, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696393, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696420, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696422, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696483, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696515, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696518, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696546, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696548, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696588, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696615, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696638, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696675, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696696, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696698, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696733, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696736, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696770, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696772, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696801, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696828, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696830, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696876, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696878, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696905, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696907, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696933, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696957, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208696989, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697017, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697018, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697056, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697084, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697086, "dur": 28, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697118, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697121, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697151, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697153, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697180, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697182, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697220, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697223, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697247, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697249, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697278, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697280, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697304, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697306, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697337, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697363, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697415, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697417, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697442, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697479, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697517, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697544, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697546, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697572, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697574, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697599, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697628, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697654, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697656, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697684, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697708, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697732, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697734, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697756, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697779, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697781, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697806, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697835, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697858, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208697860, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208698162, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208698165, "dur": 118, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208698287, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208698289, "dur": 41, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208698334, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208698336, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208698377, "dur": 89, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208698470, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900208698585, "dur": 340683, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209039311, "dur": 15, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209039331, "dur": 177, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209039514, "dur": 103, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209039619, "dur": 7604, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209047229, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209047232, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209047275, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209047277, "dur": 2713, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209049994, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209049996, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209050038, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209050042, "dur": 73, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209050118, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209050160, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209050163, "dur": 160122, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209210295, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209210300, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209210329, "dur": 20, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209210350, "dur": 1904, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209212259, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209212262, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209212300, "dur": 18, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209212319, "dur": 31131, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209243464, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209243469, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209243500, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209243504, "dur": 1792, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209245303, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209245306, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209245341, "dur": 24, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209245366, "dur": 72, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209245441, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209245464, "dur": 1196, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209246664, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209246666, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209246694, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209246696, "dur": 654, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209247357, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209247378, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209247380, "dur": 657, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209248042, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209248044, "dur": 139, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209248187, "dur": 18, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209248208, "dur": 370, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209248583, "dur": 123, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209248710, "dur": 516, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 6268, "tid": 12884901888, "ts": 1751900209249230, "dur": 8820, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 6268, "tid": 3734, "ts": 1751900209276765, "dur": 1346, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 6268, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 6268, "tid": 8589934592, "ts": 1751900208477522, "dur": 186951, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 6268, "tid": 8589934592, "ts": 1751900208664475, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 6268, "tid": 8589934592, "ts": 1751900208664483, "dur": 1001, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 6268, "tid": 3734, "ts": 1751900209278113, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 6268, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 6268, "tid": 4294967296, "ts": 1751900208390309, "dur": 869879, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 6268, "tid": 4294967296, "ts": 1751900208394585, "dur": 70905, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 6268, "tid": 4294967296, "ts": 1751900209260390, "dur": 4757, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 6268, "tid": 4294967296, "ts": 1751900209263146, "dur": 98, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 6268, "tid": 4294967296, "ts": 1751900209265219, "dur": 13, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 6268, "tid": 3734, "ts": 1751900209278118, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751900208489242, "dur": 2275, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751900208491525, "dur": 1032, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751900208492680, "dur": 77, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751900208492758, "dur": 354, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751900208494136, "dur": 748, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_85A7F339847C7C8F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751900208495393, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_58C2D2C6FDB49C32.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751900208495854, "dur": 1712, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7CC9B7F9B9108300.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751900208498022, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751900208502017, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751900208493137, "dur": 20262, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751900208513411, "dur": 733640, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751900209247054, "dur": 229, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751900209247474, "dur": 1568, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751900208493321, "dur": 20097, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208513441, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208513571, "dur": 970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_01F0438719F4637C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208514542, "dur": 605, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208515154, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_3497E18D9ACE6C53.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208515289, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208515707, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_3497E18D9ACE6C53.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208515887, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_E221DA127803E46F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208516083, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208516377, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E0C67EE87749D19A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208516449, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208516751, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_316ABDB813889A82.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208517008, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208517440, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_316ABDB813889A82.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208517528, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_5AAA2FC2CAC01A77.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208517807, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208518137, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_EFF9361EDEBC0A6E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208518255, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208518512, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_AC0F081190478124.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208518657, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208518962, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_C7ED8541370EC3E2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208519030, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208519432, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_744130CDF3C1ADFA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208519501, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208519645, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_AE7BD4FEA5F1A611.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208519749, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208520056, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208520380, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_6717AAFEBC09DAE0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208520436, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208521055, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208521239, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208521292, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751900208521882, "dur": 3202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208525084, "dur": 1646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208529410, "dur": 635, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\treeview\\Drawers\\Layers\\MarkersLayer.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751900208526730, "dur": 3493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208530646, "dur": 547, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Actions\\TrackAction.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751900208532562, "dur": 623, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Actions\\IAction.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751900208530224, "dur": 3500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208533724, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208534151, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208534767, "dur": 477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208535245, "dur": 753, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Generation\\Processors\\ShaderSpliceUtil.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751900208536456, "dur": 632, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Generation\\Processors\\PropertyCollector.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751900208535245, "dur": 4549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208539794, "dur": 1488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208541282, "dur": 1531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208543157, "dur": 536, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Graphs\\CategoryData.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751900208542813, "dur": 1945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208544759, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208545886, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208546349, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208546822, "dur": 2361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208549184, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208549627, "dur": 1561, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Variables\\Obsolete\\IsGraphVariableDefined.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751900208549627, "dur": 3424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208553051, "dur": 2912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208555963, "dur": 3373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208559336, "dur": 589, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Interface\\DragAndDrop\\DragAndDropUtility.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751900208559336, "dur": 2888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208562224, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208562818, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208563348, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208564682, "dur": 597, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Serialization\\ISerializationDepender.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751900208563890, "dur": 2947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208566837, "dur": 1777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208568614, "dur": 2633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208571247, "dur": 2497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208573745, "dur": 2143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208577939, "dur": 975, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\PendingChanges\\PendingMergeLinks\\MergeLinksListView.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751900208575889, "dur": 3428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208579317, "dur": 1916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208581234, "dur": 596, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@a0f5d16b3c82\\Editor\\UGUI\\UI\\LayoutPropertiesPreview.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751900208584108, "dur": 535, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@a0f5d16b3c82\\Editor\\UGUI\\Properties\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751900208584643, "dur": 896, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@a0f5d16b3c82\\Editor\\UGUI\\EventSystem\\PhysicsRaycasterEditor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751900208581233, "dur": 4754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208585988, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208586386, "dur": 1001, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208587389, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208587502, "dur": 1159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208588669, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208588906, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208588976, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208589200, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208589377, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208589496, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208589570, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208589735, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208589793, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751900208590194, "dur": 691, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208590893, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208590961, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208591117, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208591179, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208591244, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751900208591661, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208591958, "dur": 490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208592455, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751900208592590, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208592760, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751900208593198, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208593372, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1751900208593810, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208593899, "dur": 89, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208594473, "dur": 77724, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1751900208683640, "dur": 2058, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751900208685699, "dur": 439, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208686143, "dur": 2051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751900208688195, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208688325, "dur": 2021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751900208690347, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208690466, "dur": 1988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751900208692455, "dur": 1019, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208693484, "dur": 1959, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751900208695444, "dur": 557, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208696009, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208696081, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208696507, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208696666, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208696881, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751900208697411, "dur": 549762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208493339, "dur": 20099, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208513444, "dur": 1209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_E7120EB995DA210C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900208514654, "dur": 472, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208515132, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_455623E8771FA1DB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900208515275, "dur": 409, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208515684, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_455623E8771FA1DB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900208515867, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_85A7F339847C7C8F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900208516229, "dur": 423, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208516657, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_CE9F463A1284211A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900208516922, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208517238, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_A9459742FC2B00F9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900208517534, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208517897, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_8A562CA37CD388A9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900208518118, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208518398, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_7251D83D80DD0453.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900208518711, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208518777, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_2D8413BDE13CD7F1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900208518885, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208519211, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_C3A5EBA3221A6287.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900208519299, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208519429, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_441A482B34BB1A1B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900208519502, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208519656, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_8A77BBF0F2259C97.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900208519709, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208519913, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900208520009, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208520272, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208521038, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208521124, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208521222, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208521919, "dur": 638, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Net.ServicePoint.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751900208521918, "dur": 2153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208524071, "dur": 1932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208526003, "dur": 2431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208529382, "dur": 724, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Manipulators\\Trim\\TrimItemModeRipple.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751900208528434, "dur": 3185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208531619, "dur": 1735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208533354, "dur": 979, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center@f3fb577b3546\\Editor\\MultiplayerCenterWindow\\UI\\RecommendationView\\SolutionSelectionView.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751900208535941, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center@f3fb577b3546\\Editor\\MultiplayerCenterWindow\\RecommendationTabView.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751900208536467, "dur": 632, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center@f3fb577b3546\\Editor\\MultiplayerCenterWindow\\MultiplayerCenterWindow.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751900208533354, "dur": 5044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208538399, "dur": 635, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Drawing\\Inspector\\PropertyDrawers\\TransformNodePropertyDrawer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751900208538399, "dur": 3352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208543138, "dur": 523, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Interfaces\\IMayRequirePositionPredisplacement.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751900208541751, "dur": 2267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208544018, "dur": 2797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208546815, "dur": 2494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208549310, "dur": 505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208550342, "dur": 1011, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Nulls\\NullCoalesce.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751900208549815, "dur": 2794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208552609, "dur": 2866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208555827, "dur": 512, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Widgets\\Nodes\\NodeWidget.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751900208555476, "dur": 3107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208560021, "dur": 560, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Meta\\RootMetadata.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751900208558583, "dur": 3320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208561904, "dur": 1676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208563580, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208564047, "dur": 3554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208567601, "dur": 2062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208569663, "dur": 2340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208572004, "dur": 2236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208576061, "dur": 571, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Controls\\Processors\\InvertVector3Processor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751900208576807, "dur": 543, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Controls\\Processors\\CompensateRotationProcessor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751900208574240, "dur": 3522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208577763, "dur": 1082, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\UI\\StatusBar\\StatusBar.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751900208577763, "dur": 2832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208580596, "dur": 1676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208582273, "dur": 836, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@dfdbd02f5918\\UnityEditor.TestRunner\\TestRun\\Tasks\\CleanupVerificationTask.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751900208582273, "dur": 3444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208585717, "dur": 661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208586379, "dur": 798, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208587179, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900208587370, "dur": 1234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208588610, "dur": 1222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751900208589833, "dur": 666, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208590509, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208590568, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900208590859, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208591070, "dur": 1046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751900208592117, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208592558, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208592700, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900208592797, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208592856, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751900208593633, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208593878, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208593938, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751900208594029, "dur": 431, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208594464, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751900208595071, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208595374, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208595459, "dur": 3706, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208599166, "dur": 84496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208683663, "dur": 2190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751900208685854, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208686219, "dur": 2040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751900208688259, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208688398, "dur": 2130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751900208690529, "dur": 610, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208691145, "dur": 2546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751900208693692, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208693812, "dur": 3228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751900208697041, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751900208697162, "dur": 549880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208493424, "dur": 20072, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208513501, "dur": 1102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_B8BEB69DE53F9C41.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900208514604, "dur": 519, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208515130, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_B428977015ACB395.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900208515268, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208515635, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_B428977015ACB395.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900208515768, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_2E8623D16E8E668C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900208516056, "dur": 815, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208516876, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_0BCE78C25C474DFA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900208517140, "dur": 389, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208517535, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_1FFECC68464BBF36.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900208517875, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208518138, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_58C2D2C6FDB49C32.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900208518339, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208518708, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_2B2A94CF86E88A71.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900208518991, "dur": 440, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208519435, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_EDF98A79C83CDC6C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900208519487, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208519639, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B25B7B96E5346C1F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900208519693, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208519989, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7CC9B7F9B9108300.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900208520087, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208520315, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208520515, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208520631, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208520781, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208520862, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208521096, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208521207, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208521416, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751900208521884, "dur": 2578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208524462, "dur": 1841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208526303, "dur": 2527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208528831, "dur": 1030, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Manipulators\\Sequence\\EaseClip.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751900208528831, "dur": 5540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208534372, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208535323, "dur": 660, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Util\\UIUtilities.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751900208536430, "dur": 775, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Util\\MessageManager.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751900208535046, "dur": 2390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208537437, "dur": 1313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208538750, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208540123, "dur": 577, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Nodes\\Math\\Range\\RandomRangeNode.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751900208540092, "dur": 2632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208543052, "dur": 610, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Graphs\\GroupData.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751900208542725, "dur": 2223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208544948, "dur": 624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208545573, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208546000, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208546446, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208546929, "dur": 2904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208550526, "dur": 739, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector3\\Vector3Sum.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751900208549833, "dur": 1922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208551756, "dur": 2413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208554170, "dur": 2360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208556531, "dur": 3129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208559660, "dur": 2639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208562305, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208562388, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208562835, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208563401, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208563816, "dur": 2896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208566713, "dur": 3284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208570262, "dur": 641, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework.performance@92d1d09a72ed\\Runtime\\Measurements\\ScopeMeasurement.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751900208569998, "dur": 2727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208572725, "dur": 2338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208575446, "dur": 508, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\random.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751900208575063, "dur": 1745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208576809, "dur": 617, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\Locks\\LocksListHeaderState.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751900208578259, "dur": 898, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\Diff\\UnityDiffTree.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751900208576809, "dur": 2532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208579840, "dur": 727, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Inspector\\InspectorAssetSelection.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751900208581019, "dur": 520, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Hub\\Operations\\OperationParams.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751900208579341, "dur": 3057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208582399, "dur": 578, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@dfdbd02f5918\\UnityEditor.TestRunner\\TestLaunchers\\RemotePlayerLogController.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751900208582399, "dur": 2524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208584924, "dur": 1451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208586375, "dur": 804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208587189, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900208587379, "dur": 1366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208588752, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751900208589325, "dur": 411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208589743, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208590052, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751900208590810, "dur": 572, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208591392, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208591800, "dur": 650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208592451, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900208592585, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208592738, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751900208593077, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208593301, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208593379, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208593720, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900208593850, "dur": 571, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208594428, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751900208594829, "dur": 666, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208595504, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208595567, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900208595701, "dur": 583, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208596290, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751900208596886, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208597167, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208597246, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751900208597368, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751900208597663, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208598033, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208598197, "dur": 962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208599159, "dur": 72420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208671580, "dur": 12069, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208683669, "dur": 1962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751900208685632, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208685952, "dur": 2508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751900208688461, "dur": 452, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208688924, "dur": 2042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751900208690967, "dur": 643, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208691616, "dur": 2042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751900208693659, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208693818, "dur": 2090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751900208695909, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208696005, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208696113, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208696168, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208696234, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208696370, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208696470, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751900208696549, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208696861, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751900208696985, "dur": 550060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208493328, "dur": 20102, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208513443, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208513574, "dur": 1041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_31AB2D3F1E4A3D66.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900208514615, "dur": 785, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208515404, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_409E75408550DFD9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900208515678, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208515938, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_B3BE1B8334830D52.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900208516230, "dur": 621, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208516856, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9AB55CF056145574.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900208517172, "dur": 493, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208517666, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9AB55CF056145574.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900208517747, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7C053C2C91326FF8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900208518041, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208518400, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_C5267BC5A847D8B9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900208518673, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208518943, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_94EF8D6143DA44EF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900208519003, "dur": 434, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208519504, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_70F846A33051C353.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900208519574, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208519957, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208520107, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208521030, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208521188, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751900208521320, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208521469, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208521696, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208521772, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208521915, "dur": 571, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751900208521915, "dur": 2078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208523994, "dur": 1797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208525792, "dur": 3131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208528924, "dur": 939, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\inspectors\\BuiltInCurvePresets.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751900208532729, "dur": 544, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Audio\\AudioTrackInspector.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751900208528924, "dur": 4531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208533455, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208534587, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208535968, "dur": 1122, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Serialization\\MultiJsonInternal.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751900208535107, "dur": 2166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208537273, "dur": 1489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208538763, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208539973, "dur": 2488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208543100, "dur": 589, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Graphs\\SamplerStateShaderProperty.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751900208542461, "dur": 2464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208544926, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208545526, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208546040, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208546547, "dur": 2999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208550192, "dur": 933, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Ports\\IUnitOutputPortDefinition.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751900208549547, "dur": 3042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208553628, "dur": 504, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Lifecycle\\Update.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751900208552589, "dur": 3353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208555943, "dur": 2684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208558627, "dur": 3540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208562168, "dur": 1239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208563407, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208564124, "dur": 2517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208566642, "dur": 3016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208569659, "dur": 2681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208572340, "dur": 2182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208574522, "dur": 2977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208577499, "dur": 1206, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\UI\\UnityPlasticTimer.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751900208577499, "dur": 3007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208580507, "dur": 3015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208583522, "dur": 2075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208585598, "dur": 783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208586381, "dur": 794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208587177, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900208587455, "dur": 1109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208588569, "dur": 728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751900208589298, "dur": 1058, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208590373, "dur": 689, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208591069, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900208591221, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208591467, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751900208592016, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208592301, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208592447, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900208592580, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208592735, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751900208593350, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208593559, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208593719, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900208593855, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208593910, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751900208594220, "dur": 524, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208594755, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208594915, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900208595075, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208595264, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751900208595829, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208595955, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208596324, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751900208596415, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208596583, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751900208596867, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208597133, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208597196, "dur": 1971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208599167, "dur": 84468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208683645, "dur": 2023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751900208685670, "dur": 456, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208686133, "dur": 2228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751900208688366, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208688721, "dur": 2126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751900208690848, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208691228, "dur": 1993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751900208693222, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208693573, "dur": 2070, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751900208695643, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208695746, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208695946, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208696052, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208696166, "dur": 623, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208696862, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751900208697171, "dur": 549876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208493373, "dur": 20075, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208513454, "dur": 1079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_DD3CBC65A51B3B4A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900208514535, "dur": 822, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208515365, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_A52D67D727C640CA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900208515633, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208515942, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_17831032FF1C6E20.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900208516536, "dur": 443, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208516980, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_17831032FF1C6E20.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900208517061, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_334958EB079AC540.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900208517406, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208517690, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_C81CFFEB4B719421.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900208518029, "dur": 440, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208518472, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_982F16DC875BE786.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900208518660, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208518851, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_72CE526178B0FB4C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900208518937, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208519269, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_3D6AFCA43E0B6878.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900208519340, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208519492, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_F03F48A1E46251D9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900208519606, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208519862, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208519941, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900208520018, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208520393, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208520594, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751900208521416, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751900208521894, "dur": 653, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Windows.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751900208521894, "dur": 2364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208524259, "dur": 1522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208525782, "dur": 2383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208528165, "dur": 3211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208531376, "dur": 3505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208534881, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208535356, "dur": 662, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Generation\\Descriptors\\KeywordDescriptor.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751900208535355, "dur": 2147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208537502, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Drawing\\Views\\IShaderNodeView.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751900208537502, "dur": 2908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208540411, "dur": 2505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208542916, "dur": 776, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\AssetCallbacks\\CreateShaderGraph.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751900208542916, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208544162, "dur": 3215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208549033, "dur": 550, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Flow\\FlowMachineEditor.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751900208549725, "dur": 908, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Flow\\FlowGraphEditor.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751900208547377, "dur": 4055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208551433, "dur": 786, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Scalar\\ScalarMaximum.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751900208551432, "dur": 3574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208555006, "dur": 2518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208557525, "dur": 2623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208560148, "dur": 3102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208563251, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208563768, "dur": 1943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208565711, "dur": 1711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208567423, "dur": 757, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Cloning\\ICloner.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751900208567423, "dur": 3043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208570467, "dur": 2212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208574102, "dur": 522, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Editor\\Internal\\AdvancedDropdown\\MultiLevelDataSource.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751900208572679, "dur": 2693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208576083, "dur": 537, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\double2x4.gen.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751900208575372, "dur": 1844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208577961, "dur": 885, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\Branch\\Dialogs\\RenameBranchDialog.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751900208577216, "dur": 2394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208579610, "dur": 2504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208582546, "dur": 593, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@dfdbd02f5918\\UnityEditor.TestRunner\\TestRunner\\Messages\\WaitForDomainReload.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751900208584160, "dur": 611, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@dfdbd02f5918\\UnityEditor.TestRunner\\TestRunner\\Callbacks\\WindowResultUpdaterDataHolder.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751900208584833, "dur": 580, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@dfdbd02f5918\\UnityEditor.TestRunner\\TestRun\\WaitForPlayerRunTask.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751900208582114, "dur": 3927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208586041, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208586369, "dur": 814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208587184, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900208587397, "dur": 1883, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208589287, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751900208589810, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208590083, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208590372, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900208590571, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751900208590973, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208591117, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208591175, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900208591372, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751900208591813, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208592048, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208592205, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208592457, "dur": 914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208593372, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208593726, "dur": 2090, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208595819, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751900208595900, "dur": 630, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208596558, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751900208596987, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208597220, "dur": 1942, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208599163, "dur": 84475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208683640, "dur": 1991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751900208685632, "dur": 441, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208686081, "dur": 2061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751900208688142, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208688425, "dur": 2569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751900208690995, "dur": 501, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208691516, "dur": 2309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751900208693826, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208693932, "dur": 2614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751900208696547, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208696721, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900208696892, "dur": 547363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751900209244298, "dur": 1267, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751900209244257, "dur": 1310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751900209245607, "dur": 1521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208493408, "dur": 20049, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208513464, "dur": 1117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_36D94D0B0882E7EC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900208514582, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208515031, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_D09D54285D3D6DCD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900208515163, "dur": 488, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208515651, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_D09D54285D3D6DCD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900208515778, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_201A952097D16B89.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900208516119, "dur": 390, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208516514, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_A3DF7B62ADBC8E51.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900208516689, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208517017, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_E8798D0BA79A4C08.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900208517415, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208517668, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_129E2D525C6714B0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900208518082, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208518351, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_BBF013107B6E7683.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900208518533, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208518754, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_7FE145A456DFCBEF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900208518889, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208519284, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_0BFC51EF888A92A4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900208519346, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208519503, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_14BBB0A1A56B54F9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900208519623, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208519869, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900208519926, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208520370, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_7E6A236E3ABEDC63.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900208520437, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208521913, "dur": 656, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751900208521913, "dur": 2785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208524698, "dur": 1793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208528915, "dur": 1007, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Utilities\\CustomTrackDrawerAttribute.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751900208526491, "dur": 3766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208530258, "dur": 4324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208534582, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208535206, "dur": 1030, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Generation\\Targets\\CustomRenderTexture\\CustomTextureSelf.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751900208536475, "dur": 617, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Generation\\Targets\\CustomRenderTexture\\CreateCustomRenderTextureShaderGraph.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751900208535152, "dur": 3270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208538422, "dur": 657, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Drawing\\Inspector\\PropertyDrawers\\FloatPropertyDrawer.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751900208538422, "dur": 3056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208543012, "dur": 605, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Nodes\\Artistic\\Filter\\FadeTransitionNode.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751900208541479, "dur": 2525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208544004, "dur": 673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208544678, "dur": 2638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208549540, "dur": 662, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Flow\\Framework\\Codebase\\SetMemberOption.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751900208550202, "dur": 777, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Flow\\Framework\\Codebase\\SetMemberDescriptor.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751900208547316, "dur": 4650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208551966, "dur": 2923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208554890, "dur": 1984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208556874, "dur": 515, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Plugin\\Migrations\\Migration_1_5_1_to_1_5_2.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751900208556874, "dur": 2833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208559707, "dur": 567, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Inspection\\MetadataCollectionAdaptor.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751900208559707, "dur": 3348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208563055, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208563709, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208564243, "dur": 2514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208566757, "dur": 2707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208569464, "dur": 2121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208571586, "dur": 2784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208574370, "dur": 2270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208578026, "dur": 667, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\Merge\\Developer\\MergeOptionsDialog.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751900208576641, "dur": 2307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208578948, "dur": 2268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208582541, "dur": 551, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@a0f5d16b3c82\\Editor\\UGUI\\UI\\RectMask2DEditor.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751900208581217, "dur": 3345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208584563, "dur": 1826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208586391, "dur": 793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208587185, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900208587366, "dur": 1293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208588663, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751900208589273, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208589561, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208589744, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751900208590269, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208590590, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751900208591002, "dur": 596, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208591610, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208591744, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208591796, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208592456, "dur": 920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208593376, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208593725, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751900208593871, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208594255, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751900208594703, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208594884, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208595071, "dur": 4087, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208599158, "dur": 65284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208668831, "dur": 200, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1751900208669032, "dur": 2478, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 6, "ts": 1751900208671510, "dur": 60, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 6, "ts": 1751900208664444, "dur": 7131, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208671575, "dur": 12068, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208683649, "dur": 2286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751900208685936, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208686106, "dur": 1941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751900208688048, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208688277, "dur": 2085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751900208690363, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208690485, "dur": 1698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751900208692184, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208692283, "dur": 2088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751900208694372, "dur": 951, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208695344, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208695578, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208695939, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208696048, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208696287, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208696380, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208696492, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208696696, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208696793, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900208696888, "dur": 545220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751900209242144, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751900209242115, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751900209242294, "dur": 1939, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751900209244241, "dur": 2911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208493440, "dur": 20154, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208513613, "dur": 1175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_F016717A46FE59BB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900208514789, "dur": 448, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208515241, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3B4D13D712F00EB8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900208515421, "dur": 419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208515893, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_12994AB43863D35B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900208516082, "dur": 495, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208516584, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_66BBE97858D0953F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900208516740, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208517138, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_1A6A88D66EF4C557.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900208517437, "dur": 541, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208517982, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_74816C903F3AF197.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900208518168, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208518426, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_657053D7FE219A28.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900208518653, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208518844, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CE693D9B51C79752.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900208518904, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208519217, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_84C61B5FFF6AE5F9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900208519335, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208519487, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_88B40F493262C216.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900208519616, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208519808, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208519920, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900208520016, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208520374, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208520616, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751900208520727, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751900208520834, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751900208520894, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208521002, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751900208521098, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208521917, "dur": 547, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751900208521917, "dur": 2268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208524186, "dur": 2144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208526330, "dur": 2646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208528977, "dur": 1088, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Attributes\\TimelineShortcutAttribute.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751900208528977, "dur": 3775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208533629, "dur": 595, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center@f3fb577b3546\\Editor\\Recommendations\\RecommenderSystem.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751900208535252, "dur": 802, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center@f3fb577b3546\\Editor\\Questionnaire\\UserChoicesObject.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751900208536717, "dur": 682, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center@f3fb577b3546\\Editor\\OnBoarding\\SectionsFinder.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751900208532752, "dur": 5479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208538231, "dur": 3283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208541514, "dur": 1541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208543132, "dur": 561, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a55da47cc43f\\Runtime\\2D\\Shadows\\ShadowCaster2D.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751900208543055, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208544156, "dur": 3655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208547811, "dur": 2358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208550170, "dur": 1147, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector3\\Vector3Lerp.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751900208551765, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector3\\DeprecatedVector3Add.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751900208550170, "dur": 2836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208553006, "dur": 3339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208556345, "dur": 3531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208559877, "dur": 706, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Graph\\LudiqGraphsEditorUtility.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751900208559876, "dur": 3290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208563166, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208564229, "dur": 2679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208567624, "dur": 558, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Decorators\\ValueAttribute.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751900208566909, "dur": 2727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208569636, "dur": 1677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208571313, "dur": 2305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208573619, "dur": 1870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208575490, "dur": 2363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208577854, "dur": 999, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\UI\\HandleMenuItem.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751900208577854, "dur": 3623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208581481, "dur": 2728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208584210, "dur": 83, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208584482, "dur": 1901, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208586384, "dur": 807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208587197, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900208587388, "dur": 1366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208588757, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751900208589337, "dur": 424, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208589771, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208589932, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751900208590498, "dur": 860, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208591369, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208591476, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208591773, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208592453, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900208592551, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208592691, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751900208593133, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208593263, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208593374, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208593736, "dur": 5420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208599157, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751900208599273, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208599326, "dur": 84325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208683652, "dur": 1884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751900208685538, "dur": 682, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208686224, "dur": 2045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751900208688270, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208688513, "dur": 2227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751900208690741, "dur": 461, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208691225, "dur": 3411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751900208694637, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208694785, "dur": 2288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751900208697073, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751900208697387, "dur": 549778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208493465, "dur": 20179, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208513668, "dur": 925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_AC251BD94F5FD3AE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900208514593, "dur": 409, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208515029, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_57398B720CBF96BC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900208515100, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208515412, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_DF6F7FF917E347B3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900208515685, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208516107, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_DF6F7FF917E347B3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900208516169, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_29BCCE2A849A4ABC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900208516246, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208516639, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_0CAABF378ACEABEF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900208516894, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208517221, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_8824D3AD2222F26E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900208517500, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208517803, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_1F0AB14E18D23E11.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900208518086, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208518390, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_B3100D568D19DE53.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900208518665, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208518882, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_8E87AC3CC97FF6E8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900208518953, "dur": 503, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208519487, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_BC1462186899290E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900208519583, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208519782, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751900208519838, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208519891, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900208520273, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208520452, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900208520569, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208520799, "dur": 37474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751900208558274, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208558731, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208559016, "dur": 2870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208563383, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@a0f5d16b3c82\\Runtime\\TMP\\TMP_SpriteAsset.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751900208561886, "dur": 2833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208564719, "dur": 1672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208566392, "dur": 3699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208570092, "dur": 683, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Utilities\\Observables\\WhereObservable.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751900208570092, "dur": 2287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208572379, "dur": 2291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208576006, "dur": 530, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\uint4x4.gen.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751900208574670, "dur": 2220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208578152, "dur": 593, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\ConfirmContinueWithPendingChangesDialog.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751900208578745, "dur": 664, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\Changesets\\LaunchDiffOperations.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751900208576890, "dur": 2776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208580002, "dur": 504, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\AssetsUtils\\Processor\\WorkspaceOperationsMonitor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751900208581113, "dur": 549, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\AssetOverlays\\DrawAssetOverlay.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751900208579666, "dur": 2587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208582453, "dur": 534, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@dfdbd02f5918\\UnityEditor.TestRunner\\TestRun\\Tasks\\Platform\\PlatformSpecificCleanupTask.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751900208584774, "dur": 522, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@dfdbd02f5918\\UnityEditor.TestRunner\\TestRun\\Tasks\\Events\\RegisterCallbackDelegatorEventsTask.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751900208582254, "dur": 3777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208586032, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208586373, "dur": 820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208587196, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900208587351, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208587608, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751900208587938, "dur": 1168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208589118, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208589285, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900208589445, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208589611, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900208589792, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208589845, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751900208590332, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208590570, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751900208590709, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208590924, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751900208591312, "dur": 831, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208592187, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208592467, "dur": 901, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208593424, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208593731, "dur": 5432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208599164, "dur": 84489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208683656, "dur": 2254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751900208685911, "dur": 444, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208686366, "dur": 2169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751900208688536, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208688806, "dur": 2040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751900208690847, "dur": 1373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208692231, "dur": 2571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751900208694803, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208695010, "dur": 2203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751900208697214, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751900208697305, "dur": 549894, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208493499, "dur": 20166, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208513673, "dur": 948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_59C0854D1BDEB3BB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751900208514622, "dur": 408, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208515037, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_4D4853D2BE4AE8AE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751900208515277, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208515528, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_22C73FD68F7437D2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751900208515869, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208516130, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_22C73FD68F7437D2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751900208516249, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_00FC4C225939AAE2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751900208516394, "dur": 673, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208517071, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_810264E72E8EDE2D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751900208517370, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208517767, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_A7499BA3B50749DD.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751900208518061, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208518440, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_BBA4FF1108562044.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751900208518685, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208518979, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_284B36A6DA56D348.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751900208519057, "dur": 448, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208519509, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_6126AAA2007E3BC1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751900208519603, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208519855, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7F371AF1A9E7F3FC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751900208519923, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208520355, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751900208520551, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751900208520652, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208520914, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208521093, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208521155, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751900208521491, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208521924, "dur": 586, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.IO.Pipes.AccessControl.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751900208521924, "dur": 2396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208524320, "dur": 2219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208526540, "dur": 549, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Utilities\\AnimatedParameterUtility.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751900208529535, "dur": 516, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\treeview\\TimelineDragging.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751900208526540, "dur": 4028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208533449, "dur": 626, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@a0f5d16b3c82\\Editor\\TMP\\PropertyDrawers\\TMP_TextAlignmentDrawer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751900208530568, "dur": 4214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208534782, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208535252, "dur": 707, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Generation\\Enumerations\\RenderQueue.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751900208535252, "dur": 3556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208538808, "dur": 1574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208540382, "dur": 1692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208543092, "dur": 592, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Graphs\\VertexColorMaterialSlot.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751900208542075, "dur": 2699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208544774, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208545365, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208545867, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208546316, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208546742, "dur": 2456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208549198, "dur": 633, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.rendering.light-transport@9bd588f963c0\\Runtime\\Sampling\\SobolData.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751900208549198, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208550225, "dur": 1069, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector2\\Vector2Lerp.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751900208551701, "dur": 545, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector2\\DeprecatedVector2Add.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751900208550225, "dur": 4124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208554349, "dur": 2457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208556807, "dur": 3166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208559974, "dur": 604, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Description\\IGraphElementDescription.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751900208559974, "dur": 3866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208563840, "dur": 2750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208566590, "dur": 3378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208569969, "dur": 2036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208572006, "dur": 1343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208575511, "dur": 604, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Devices\\Precompiled\\FastTouchscreen.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751900208573349, "dur": 3100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208576764, "dur": 581, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\PendingChanges\\FilesFilterPatternsMenuBuilder.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751900208577964, "dur": 779, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\PendingChanges\\Dialogs\\FilterRulesConfirmationDialog.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751900208576450, "dur": 3542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208579992, "dur": 2047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208582040, "dur": 824, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@dfdbd02f5918\\UnityEditor.TestRunner\\TestSettings\\TestSettingsDeserializer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751900208584654, "dur": 648, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@dfdbd02f5918\\UnityEditor.TestRunner\\TestRunner\\Utils\\EditorAssemblyWrapper.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751900208582040, "dur": 3498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208585538, "dur": 825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208586367, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208586445, "dur": 761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208587207, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751900208587351, "dur": 1316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208588680, "dur": 1261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751900208589942, "dur": 524, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208590473, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751900208590866, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208591073, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751900208591223, "dur": 455, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208591682, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751900208592080, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208592391, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208592541, "dur": 830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208593371, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208593730, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751900208593872, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208594313, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751900208594783, "dur": 846, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208595648, "dur": 427, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208596084, "dur": 3070, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208599155, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751900208599263, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208599394, "dur": 84265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208683662, "dur": 3732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751900208687395, "dur": 803, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208688210, "dur": 2188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751900208690399, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208690566, "dur": 1903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751900208692475, "dur": 553, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208693040, "dur": 2166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751900208695206, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208695559, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208695617, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208695806, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208695870, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208695956, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208696103, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208696175, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208696408, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1751900208696578, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208696655, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208696864, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751900208697329, "dur": 549882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208493529, "dur": 20147, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208513684, "dur": 980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B76B86F6FFE10D96.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751900208514664, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208515047, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_B430B0CD9867C792.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751900208515178, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208515426, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_F9331591B1619B3E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751900208515774, "dur": 557, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208516335, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_87AA2ADA08DFF7E1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751900208516580, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208516773, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_A42BD3F60EFF66D9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751900208517104, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208517408, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_A42BD3F60EFF66D9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751900208517494, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_F12DCDB48BAEBA17.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751900208517881, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208518139, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_31B99CCAA2C60608.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751900208518281, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208518504, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_C7086B172735D898.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751900208518663, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208518935, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_32939C22C21A9A23.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751900208519015, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208519525, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_728920FEC952B3E5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751900208519609, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208520154, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208520278, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751900208520376, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_4B16FAA5016376E3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751900208520457, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208520855, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751900208521387, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208521784, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16285879237017002407.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751900208521930, "dur": 626, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.51f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751900208521930, "dur": 2581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208524512, "dur": 1842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208526354, "dur": 2514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208528868, "dur": 528, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Items\\ItemsGroup.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751900208529469, "dur": 547, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\inspectors\\TrackAssetInspector.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751900208532052, "dur": 1281, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\inspectors\\CurvesOwner\\ICurvesOwnerInspectorWrapper.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751900208528868, "dur": 4895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208533763, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208534087, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208534482, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208535117, "dur": 1121, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Interface\\IRequiresData.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751900208536238, "dur": 918, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Interface\\IConditional.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751900208537228, "dur": 635, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Importers\\ShaderSubGraphImporterEditor.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751900208535117, "dur": 4975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208540092, "dur": 2266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208543016, "dur": 683, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Graphs\\Texture2DMaterialSlot.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751900208542359, "dur": 2557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208544917, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208545484, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208545972, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208546408, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208546847, "dur": 2636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208550308, "dur": 658, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\ScriptMachine.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751900208549483, "dur": 2896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208552380, "dur": 508, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Rendering\\OnBecameVisible.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751900208552380, "dur": 2720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208555960, "dur": 532, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Editor\\VisualScripting.Core\\Windows\\GeneratePropertyProvidersWindow\\GeneratePropertyProvidersPage.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751900208555100, "dur": 2959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208558059, "dur": 3316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208561375, "dur": 2560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208563935, "dur": 3012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208566948, "dur": 3142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208570090, "dur": 2293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208572384, "dur": 2851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208575236, "dur": 743, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\half4.gen.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751900208577581, "dur": 1205, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\float3x4.gen.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751900208575236, "dur": 4143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208580133, "dur": 515, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Developer\\UpdateReport\\UpdateReportListView.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751900208579379, "dur": 2027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208581438, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208582224, "dur": 766, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@dfdbd02f5918\\UnityEditor.TestRunner\\TestRun\\Tasks\\Scene\\StoreSceneSetupTask.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751900208582224, "dur": 2696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208584920, "dur": 1442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208586401, "dur": 787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208587189, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751900208587337, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208587649, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751900208587896, "dur": 955, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208588864, "dur": 595, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208589465, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751900208589631, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208589690, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751900208590261, "dur": 463, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208590729, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751900208590864, "dur": 508, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208591376, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751900208591983, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208592227, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751900208592328, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751900208592613, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208592884, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208593373, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208593446, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208593729, "dur": 2601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208596331, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751900208596431, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208596610, "dur": 1139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751900208597750, "dur": 403, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208598164, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208598304, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751900208598384, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208598505, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751900208598906, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208599161, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751900208599256, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208599371, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751900208599593, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900208599719, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751900208601249, "dur": 436705, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751900209045989, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751900209045561, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751900209046154, "dur": 1276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751900209048874, "dur": 179, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751900209049842, "dur": 161357, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751900209244275, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1751900209244251, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1751900209244396, "dur": 2748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208493550, "dur": 20137, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208513693, "dur": 861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_172A8F2500200F04.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751900208514555, "dur": 561, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208515124, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_B147A254CFD2102C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751900208515275, "dur": 377, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208515653, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_B147A254CFD2102C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751900208515768, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_D8824977E7F2B23E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751900208515998, "dur": 818, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208516822, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_C89BF2893952DD2D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751900208517118, "dur": 442, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208517566, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_7B597B5EE040FE53.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751900208518000, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208518172, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_91BF7E65A37C8C47.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751900208518324, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208518563, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_0A8CC276CBDE6D9D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751900208518822, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208519219, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_403651568F253731.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751900208519301, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208519484, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_FFA66C83E76ECCA7.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751900208519577, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208519718, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_36711FC8F966584C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751900208519779, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208520180, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_8C9D4507428A9FC0.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751900208520237, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208521042, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208521349, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751900208521450, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208521880, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208523106, "dur": 2005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208525112, "dur": 2454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208529393, "dur": 611, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\State\\WindowState.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751900208527567, "dur": 3210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208530777, "dur": 683, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@a0f5d16b3c82\\Editor\\TMP\\PropertyDrawers\\TMP_MarkToBaseAdjustmentRecordPropertyDrawer.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751900208530777, "dur": 4393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208535170, "dur": 803, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGraph\\Targets\\BuiltInCanvasSubTarget.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751900208535170, "dur": 3667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208538837, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208540012, "dur": 616, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Nodes\\Math\\Trigonometry\\TangentNode.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751900208539975, "dur": 1903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208543134, "dur": 540, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Interfaces\\Graph\\IOnAssetEnabled.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751900208541879, "dur": 2143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208544023, "dur": 2842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208546865, "dur": 2060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208548926, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208549590, "dur": 618, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\MultiInputUnit.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751900208550209, "dur": 999, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\IUnitDebugData.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751900208549590, "dur": 4268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208553859, "dur": 512, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Flow\\Framework\\Control\\SelectUnit_T.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751900208553859, "dur": 3152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208557012, "dur": 2355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208559368, "dur": 2428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208561796, "dur": 2350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208564146, "dur": 2699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208566845, "dur": 2851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208569697, "dur": 3173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208572870, "dur": 2325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208575195, "dur": 1545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208576741, "dur": 611, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\Merge\\Developer\\DirectoryConflicts\\MoveDeleteMenu.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751900208578026, "dur": 1330, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Views\\Merge\\Developer\\DirectoryConflicts\\DeleteMoveMenu.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751900208576741, "dur": 3515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208580257, "dur": 2428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208582686, "dur": 2211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208584898, "dur": 1467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208586365, "dur": 816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208587183, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751900208587384, "dur": 1280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208588672, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751900208589253, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208589594, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751900208589767, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208589828, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751900208590286, "dur": 417, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208590715, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208590836, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208590966, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751900208591090, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208591231, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751900208591644, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208591922, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208592455, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208593382, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208593723, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751900208593877, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208594253, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751900208594736, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208595072, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208595152, "dur": 4008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208599161, "dur": 84503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208683665, "dur": 2010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751900208685676, "dur": 608, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208686293, "dur": 2012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751900208688306, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208688423, "dur": 2525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751900208690948, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208691054, "dur": 2232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751900208693287, "dur": 1161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208694456, "dur": 2367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751900208696824, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751900208696970, "dur": 550079, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208493571, "dur": 20127, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208513699, "dur": 874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_B52235DE51A256C2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208514573, "dur": 437, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208515033, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_81542F95A0721D6A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208515228, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208515417, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_AF66D8329BCEF71E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208515744, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208516026, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_EF946CA7674E0325.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208516253, "dur": 503, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208516762, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_52799C164F48C7C2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208517080, "dur": 416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208517497, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_52799C164F48C7C2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208517578, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_3787BA530AC70137.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208517927, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208518392, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FC3813CDB6AE67C0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208518558, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208518746, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_2AE61BEFE92D422E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208518915, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208519214, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_C763F25127858F24.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208519281, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208519488, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_FB367C9346FF9A5D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208519556, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208519651, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_22355095CF1A36B0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208519746, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208520078, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208520211, "dur": 876, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208521092, "dur": 40919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751900208562013, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208562251, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208562316, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208562421, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208562606, "dur": 23540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751900208586147, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208586369, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208586433, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208586549, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751900208586829, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208587019, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208587180, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208587361, "dur": 1106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208588473, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751900208588950, "dur": 555, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208589515, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208589583, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208589850, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208590097, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751900208590555, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208590813, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208590875, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208591060, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208591185, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208591251, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751900208591670, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208591860, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208592453, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208592705, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208592819, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208592887, "dur": 907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751900208593795, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208593999, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208594082, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208594257, "dur": 896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751900208595154, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208595449, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208595811, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208595938, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208596319, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751900208596676, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208596963, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208597019, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751900208597157, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751900208597418, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208597786, "dur": 1380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208599166, "dur": 85077, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208684245, "dur": 2113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751900208686360, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208686462, "dur": 2014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751900208688477, "dur": 2131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208690614, "dur": 1950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751900208692565, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208692864, "dur": 2022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751900208694886, "dur": 909, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208696057, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208696135, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208696258, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208696515, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208696589, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208696680, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900208696886, "dur": 348680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900209045613, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751900209045570, "dur": 1374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751900209048558, "dur": 238, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751900209049856, "dur": 159381, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751900209242102, "dur": 4157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751900209242084, "dur": 4176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751900209246272, "dur": 715, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751900209254387, "dur": 2134, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 6268, "tid": 3734, "ts": 1751900209279286, "dur": 6781, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 6268, "tid": 3734, "ts": 1751900209286136, "dur": 2799, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 6268, "tid": 3734, "ts": 1751900209274622, "dur": 15779, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}