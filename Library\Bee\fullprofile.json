{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 9188, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 9188, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 9188, "tid": 1850, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 9188, "tid": 1850, "ts": 1751962145359005, "dur": 909, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 9188, "tid": 1850, "ts": 1751962145364071, "dur": 903, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 9188, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 9188, "tid": 1, "ts": 1751962144054695, "dur": 7199, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 9188, "tid": 1, "ts": 1751962144061899, "dur": 73900, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 9188, "tid": 1, "ts": 1751962144135808, "dur": 55107, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 9188, "tid": 1850, "ts": 1751962145364993, "dur": 75, "ph": "X", "name": "", "args": {}}, {"pid": 9188, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144052829, "dur": 9507, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144062338, "dur": 1286746, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144063664, "dur": 2832, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144066504, "dur": 1634, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068146, "dur": 218, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068411, "dur": 18, "ph": "X", "name": "ProcessMessages 20501", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068430, "dur": 36, "ph": "X", "name": "ReadAsync 20501", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068482, "dur": 1, "ph": "X", "name": "ProcessMessages 1289", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068485, "dur": 33, "ph": "X", "name": "ReadAsync 1289", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068520, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068522, "dur": 23, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068570, "dur": 61, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068635, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068637, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068674, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068675, "dur": 29, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068707, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068708, "dur": 42, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068752, "dur": 23, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068777, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068779, "dur": 23, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068804, "dur": 22, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068829, "dur": 25, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068861, "dur": 43, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068907, "dur": 1, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068921, "dur": 41, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068963, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068965, "dur": 26, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144068994, "dur": 25, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144069022, "dur": 29, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144069054, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144069061, "dur": 35, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144069097, "dur": 1, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144069099, "dur": 524, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144069626, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144069628, "dur": 96, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144069726, "dur": 6, "ph": "X", "name": "ProcessMessages 8361", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144069737, "dur": 90, "ph": "X", "name": "ReadAsync 8361", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144069829, "dur": 1, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144069831, "dur": 51, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144069883, "dur": 3, "ph": "X", "name": "ProcessMessages 1372", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144069887, "dur": 23, "ph": "X", "name": "ReadAsync 1372", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144069913, "dur": 24, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144069941, "dur": 31, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144069974, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070007, "dur": 28, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070040, "dur": 21, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070064, "dur": 22, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070124, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070126, "dur": 43, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070171, "dur": 1, "ph": "X", "name": "ProcessMessages 955", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070172, "dur": 67, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070247, "dur": 23, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070272, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070273, "dur": 20, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070296, "dur": 21, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070320, "dur": 27, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070349, "dur": 34, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070385, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070397, "dur": 69, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070470, "dur": 33, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070505, "dur": 1, "ph": "X", "name": "ProcessMessages 1513", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070507, "dur": 21, "ph": "X", "name": "ReadAsync 1513", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070530, "dur": 23, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070556, "dur": 33, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070591, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070621, "dur": 22, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070646, "dur": 21, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070670, "dur": 70, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070743, "dur": 20, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070765, "dur": 22, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070790, "dur": 24, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070816, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070818, "dur": 40, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144070861, "dur": 206, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071069, "dur": 2, "ph": "X", "name": "ProcessMessages 2572", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071072, "dur": 97, "ph": "X", "name": "ReadAsync 2572", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071172, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071207, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071209, "dur": 27, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071238, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071240, "dur": 21, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071263, "dur": 2, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071266, "dur": 23, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071292, "dur": 34, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071328, "dur": 42, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071373, "dur": 37, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071413, "dur": 1, "ph": "X", "name": "ProcessMessages 1037", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071416, "dur": 41, "ph": "X", "name": "ReadAsync 1037", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071467, "dur": 1, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071469, "dur": 31, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071504, "dur": 35, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071540, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071595, "dur": 50, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071648, "dur": 2, "ph": "X", "name": "ProcessMessages 1157", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071651, "dur": 29, "ph": "X", "name": "ReadAsync 1157", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071682, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071683, "dur": 32, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071717, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071719, "dur": 31, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071751, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071753, "dur": 29, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071784, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071786, "dur": 28, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071815, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071817, "dur": 25, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071845, "dur": 51, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071898, "dur": 1, "ph": "X", "name": "ProcessMessages 1102", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071900, "dur": 31, "ph": "X", "name": "ReadAsync 1102", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071933, "dur": 9, "ph": "X", "name": "ProcessMessages 851", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071944, "dur": 29, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144071976, "dur": 66, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072044, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072046, "dur": 41, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072089, "dur": 2, "ph": "X", "name": "ProcessMessages 1699", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072092, "dur": 34, "ph": "X", "name": "ReadAsync 1699", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072128, "dur": 1, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072129, "dur": 26, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072214, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072217, "dur": 74, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072295, "dur": 2, "ph": "X", "name": "ProcessMessages 2573", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072299, "dur": 40, "ph": "X", "name": "ReadAsync 2573", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072341, "dur": 1, "ph": "X", "name": "ProcessMessages 1204", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072357, "dur": 34, "ph": "X", "name": "ReadAsync 1204", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072392, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072395, "dur": 46, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072443, "dur": 1, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072445, "dur": 43, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072490, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072492, "dur": 27, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072522, "dur": 78, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072603, "dur": 1, "ph": "X", "name": "ProcessMessages 851", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072605, "dur": 39, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072685, "dur": 2, "ph": "X", "name": "ProcessMessages 1448", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072688, "dur": 43, "ph": "X", "name": "ReadAsync 1448", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072733, "dur": 1, "ph": "X", "name": "ProcessMessages 1825", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072736, "dur": 74, "ph": "X", "name": "ReadAsync 1825", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072812, "dur": 10, "ph": "X", "name": "ProcessMessages 102", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072824, "dur": 46, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072872, "dur": 2, "ph": "X", "name": "ProcessMessages 2341", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072875, "dur": 35, "ph": "X", "name": "ReadAsync 2341", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072911, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072913, "dur": 45, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072966, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144072968, "dur": 31, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073002, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073003, "dur": 34, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073040, "dur": 1, "ph": "X", "name": "ProcessMessages 975", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073041, "dur": 38, "ph": "X", "name": "ReadAsync 975", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073081, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073089, "dur": 72, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073163, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073165, "dur": 48, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073217, "dur": 2, "ph": "X", "name": "ProcessMessages 2176", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073220, "dur": 26, "ph": "X", "name": "ReadAsync 2176", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073258, "dur": 35, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073295, "dur": 1, "ph": "X", "name": "ProcessMessages 1004", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073298, "dur": 35, "ph": "X", "name": "ReadAsync 1004", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073374, "dur": 2, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073378, "dur": 45, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073426, "dur": 2, "ph": "X", "name": "ProcessMessages 1274", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073429, "dur": 44, "ph": "X", "name": "ReadAsync 1274", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073475, "dur": 1, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073477, "dur": 484, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073964, "dur": 1, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144073967, "dur": 99, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074143, "dur": 6, "ph": "X", "name": "ProcessMessages 8357", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074150, "dur": 77, "ph": "X", "name": "ReadAsync 8357", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074230, "dur": 2, "ph": "X", "name": "ProcessMessages 2069", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074234, "dur": 27, "ph": "X", "name": "ReadAsync 2069", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074262, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074264, "dur": 26, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074292, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074294, "dur": 21, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074318, "dur": 33, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074354, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074357, "dur": 41, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074399, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074401, "dur": 32, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074435, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074437, "dur": 42, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074482, "dur": 79, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074563, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074566, "dur": 43, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074611, "dur": 1, "ph": "X", "name": "ProcessMessages 1259", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074613, "dur": 26, "ph": "X", "name": "ReadAsync 1259", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074643, "dur": 73, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074717, "dur": 2, "ph": "X", "name": "ProcessMessages 1872", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074720, "dur": 29, "ph": "X", "name": "ReadAsync 1872", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074751, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074753, "dur": 30, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144074972, "dur": 74, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075048, "dur": 3, "ph": "X", "name": "ProcessMessages 4204", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075053, "dur": 33, "ph": "X", "name": "ReadAsync 4204", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075088, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075089, "dur": 68, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075160, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075162, "dur": 39, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075203, "dur": 1, "ph": "X", "name": "ProcessMessages 1627", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075205, "dur": 27, "ph": "X", "name": "ReadAsync 1627", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075235, "dur": 23, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075300, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075303, "dur": 44, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075349, "dur": 1, "ph": "X", "name": "ProcessMessages 1002", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075351, "dur": 34, "ph": "X", "name": "ReadAsync 1002", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075388, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075390, "dur": 27, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075419, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075420, "dur": 23, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075446, "dur": 25, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075473, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075474, "dur": 27, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075503, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075505, "dur": 39, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075549, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075594, "dur": 1, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075595, "dur": 30, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075628, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075629, "dur": 63, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075695, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075697, "dur": 27, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075726, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075728, "dur": 32, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075763, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075765, "dur": 33, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075800, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075802, "dur": 29, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075835, "dur": 30, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075868, "dur": 29, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075899, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075901, "dur": 35, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075938, "dur": 1, "ph": "X", "name": "ProcessMessages 952", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075940, "dur": 56, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144075998, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076000, "dur": 42, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076044, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076046, "dur": 33, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076081, "dur": 1, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076083, "dur": 76, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076162, "dur": 1, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076167, "dur": 47, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076216, "dur": 2, "ph": "X", "name": "ProcessMessages 2073", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076219, "dur": 32, "ph": "X", "name": "ReadAsync 2073", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076253, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076255, "dur": 36, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076293, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076295, "dur": 30, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076327, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076329, "dur": 30, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076361, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076362, "dur": 36, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076400, "dur": 1, "ph": "X", "name": "ProcessMessages 833", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076402, "dur": 34, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076438, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076440, "dur": 29, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076471, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076473, "dur": 28, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076504, "dur": 40, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076547, "dur": 1, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076550, "dur": 36, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076598, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076601, "dur": 31, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076634, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076636, "dur": 26, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076664, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076666, "dur": 23, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076692, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076724, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076726, "dur": 27, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076755, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076757, "dur": 18, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076778, "dur": 90, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076871, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076918, "dur": 1, "ph": "X", "name": "ProcessMessages 1159", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076921, "dur": 36, "ph": "X", "name": "ReadAsync 1159", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076960, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076996, "dur": 1, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144076998, "dur": 26, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077026, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077028, "dur": 52, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077084, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077123, "dur": 1, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077125, "dur": 26, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077152, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077154, "dur": 55, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077212, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077247, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077249, "dur": 29, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077281, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077283, "dur": 45, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077331, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077364, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077365, "dur": 32, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077399, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077401, "dur": 23, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077427, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077471, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077502, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077503, "dur": 31, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077596, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077599, "dur": 38, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077639, "dur": 1, "ph": "X", "name": "ProcessMessages 1108", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077641, "dur": 38, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077682, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077722, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077724, "dur": 26, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077752, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077757, "dur": 43, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077803, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077840, "dur": 1, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077842, "dur": 26, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077870, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077871, "dur": 52, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077927, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077958, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077960, "dur": 29, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077991, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144077992, "dur": 51, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078046, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078082, "dur": 1, "ph": "X", "name": "ProcessMessages 922", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078083, "dur": 57, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078142, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078144, "dur": 77, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078225, "dur": 37, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078264, "dur": 1, "ph": "X", "name": "ProcessMessages 991", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078266, "dur": 43, "ph": "X", "name": "ReadAsync 991", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078312, "dur": 1, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078314, "dur": 27, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078344, "dur": 47, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078394, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078429, "dur": 1, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078431, "dur": 27, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078460, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078461, "dur": 43, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078507, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078546, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078548, "dur": 32, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078581, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078583, "dur": 49, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078636, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078674, "dur": 1, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078676, "dur": 28, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078706, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078708, "dur": 39, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078750, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078786, "dur": 1, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078788, "dur": 26, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078816, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078818, "dur": 49, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078916, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078918, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078963, "dur": 2, "ph": "X", "name": "ProcessMessages 1499", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144078966, "dur": 49, "ph": "X", "name": "ReadAsync 1499", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079017, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079020, "dur": 58, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079080, "dur": 1, "ph": "X", "name": "ProcessMessages 938", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079082, "dur": 30, "ph": "X", "name": "ReadAsync 938", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079116, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079118, "dur": 25, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079145, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079146, "dur": 22, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079172, "dur": 42, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079216, "dur": 103, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079335, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079377, "dur": 1, "ph": "X", "name": "ProcessMessages 967", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079378, "dur": 19, "ph": "X", "name": "ReadAsync 967", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079400, "dur": 85, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079488, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079516, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079517, "dur": 30, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079558, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079561, "dur": 70, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079633, "dur": 2, "ph": "X", "name": "ProcessMessages 2031", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079636, "dur": 30, "ph": "X", "name": "ReadAsync 2031", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079669, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079719, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079764, "dur": 28, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079793, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079795, "dur": 19, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079816, "dur": 73, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079891, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079918, "dur": 25, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079946, "dur": 21, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144079970, "dur": 48, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080020, "dur": 1, "ph": "X", "name": "ProcessMessages 976", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080021, "dur": 19, "ph": "X", "name": "ReadAsync 976", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080043, "dur": 20, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080066, "dur": 84, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080153, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080181, "dur": 20, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080204, "dur": 20, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080227, "dur": 20, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080250, "dur": 80, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080339, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080374, "dur": 32, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080409, "dur": 60, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080516, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080554, "dur": 1, "ph": "X", "name": "ProcessMessages 1878", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080556, "dur": 21, "ph": "X", "name": "ReadAsync 1878", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080580, "dur": 20, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080602, "dur": 52, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080657, "dur": 21, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080681, "dur": 82, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080765, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080792, "dur": 21, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080815, "dur": 35, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080853, "dur": 61, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080917, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144080939, "dur": 224, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081165, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081167, "dur": 65, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081234, "dur": 1, "ph": "X", "name": "ProcessMessages 1368", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081236, "dur": 29, "ph": "X", "name": "ReadAsync 1368", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081268, "dur": 38, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081309, "dur": 34, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081346, "dur": 53, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081413, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081444, "dur": 36, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081483, "dur": 60, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081556, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081598, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081611, "dur": 38, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081652, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081654, "dur": 54, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081712, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081763, "dur": 1, "ph": "X", "name": "ProcessMessages 930", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081766, "dur": 29, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081799, "dur": 30, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081832, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081866, "dur": 31, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081899, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144081901, "dur": 101, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082004, "dur": 35, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082041, "dur": 1, "ph": "X", "name": "ProcessMessages 973", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082043, "dur": 26, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082073, "dur": 97, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082173, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082210, "dur": 1, "ph": "X", "name": "ProcessMessages 1179", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082213, "dur": 73, "ph": "X", "name": "ReadAsync 1179", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082293, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082295, "dur": 32, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082348, "dur": 32, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082381, "dur": 1, "ph": "X", "name": "ProcessMessages 1104", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082383, "dur": 122, "ph": "X", "name": "ReadAsync 1104", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082510, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082567, "dur": 1, "ph": "X", "name": "ProcessMessages 1185", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082570, "dur": 33, "ph": "X", "name": "ReadAsync 1185", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082610, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082613, "dur": 34, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082657, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082708, "dur": 6, "ph": "X", "name": "ProcessMessages 1141", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082717, "dur": 51, "ph": "X", "name": "ReadAsync 1141", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082781, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082785, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082836, "dur": 1, "ph": "X", "name": "ProcessMessages 1108", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082841, "dur": 29, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082872, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082873, "dur": 81, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144082958, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083000, "dur": 6, "ph": "X", "name": "ProcessMessages 973", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083007, "dur": 43, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083078, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083081, "dur": 39, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083136, "dur": 43, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083182, "dur": 1, "ph": "X", "name": "ProcessMessages 1106", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083184, "dur": 43, "ph": "X", "name": "ReadAsync 1106", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083232, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083272, "dur": 1, "ph": "X", "name": "ProcessMessages 952", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083275, "dur": 32, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083310, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083312, "dur": 52, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083379, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083434, "dur": 1, "ph": "X", "name": "ProcessMessages 1242", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083437, "dur": 37, "ph": "X", "name": "ReadAsync 1242", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083482, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083515, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083517, "dur": 23, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083621, "dur": 27, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083652, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083706, "dur": 41, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083749, "dur": 1, "ph": "X", "name": "ProcessMessages 941", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083761, "dur": 36, "ph": "X", "name": "ReadAsync 941", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083801, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083842, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083854, "dur": 29, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083885, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083887, "dur": 68, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144083965, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084003, "dur": 1, "ph": "X", "name": "ProcessMessages 1125", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084015, "dur": 45, "ph": "X", "name": "ReadAsync 1125", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084069, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084100, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084101, "dur": 25, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084137, "dur": 42, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084185, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084187, "dur": 36, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084230, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084281, "dur": 1, "ph": "X", "name": "ProcessMessages 952", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084283, "dur": 32, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084324, "dur": 23, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084350, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084411, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084413, "dur": 51, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084471, "dur": 2, "ph": "X", "name": "ProcessMessages 1132", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084473, "dur": 55, "ph": "X", "name": "ReadAsync 1132", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084536, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084550, "dur": 36, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084590, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084627, "dur": 1, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084629, "dur": 61, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084694, "dur": 2, "ph": "X", "name": "ProcessMessages 1086", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084697, "dur": 42, "ph": "X", "name": "ReadAsync 1086", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084741, "dur": 2, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084744, "dur": 30, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084778, "dur": 77, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084863, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084883, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084951, "dur": 1, "ph": "X", "name": "ProcessMessages 1353", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084954, "dur": 26, "ph": "X", "name": "ReadAsync 1353", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084982, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144084983, "dur": 44, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144085037, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144085039, "dur": 25, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144085079, "dur": 19, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144085105, "dur": 71, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144085183, "dur": 181, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144085369, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144085371, "dur": 108, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144085491, "dur": 365, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144085859, "dur": 386, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086251, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086253, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086347, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086349, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086390, "dur": 6, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086397, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086501, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086504, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086552, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086561, "dur": 44, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086608, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086611, "dur": 40, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086653, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086671, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086705, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086707, "dur": 56, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086765, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086768, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086800, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086810, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086843, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086850, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086884, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086899, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086926, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144086936, "dur": 60, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087011, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087013, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087069, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087100, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087102, "dur": 39, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087144, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087188, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087206, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087252, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087254, "dur": 37, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087299, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087303, "dur": 47, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087357, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087358, "dur": 73, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087464, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087467, "dur": 39, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087508, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087511, "dur": 50, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087570, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087572, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087601, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087603, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087635, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087664, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087666, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087699, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087830, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144087832, "dur": 239, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088075, "dur": 31, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088110, "dur": 50, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088166, "dur": 4, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088185, "dur": 38, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088226, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088228, "dur": 44, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088285, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088288, "dur": 44, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088334, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088336, "dur": 33, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088371, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088374, "dur": 47, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088427, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088430, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088471, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088482, "dur": 39, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088525, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088534, "dur": 31, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088575, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088578, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088616, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088619, "dur": 41, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088672, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088675, "dur": 29, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088722, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088730, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088764, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088767, "dur": 30, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088806, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088814, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088856, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088860, "dur": 29, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088899, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144088907, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089103, "dur": 10, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089114, "dur": 53, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089209, "dur": 4, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089216, "dur": 38, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089256, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089260, "dur": 30, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089297, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089300, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089343, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089345, "dur": 72, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089422, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089486, "dur": 2, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089503, "dur": 40, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089551, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089554, "dur": 18, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089582, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089596, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089634, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089642, "dur": 37, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089685, "dur": 7, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089694, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089723, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089725, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089756, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089759, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089826, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089829, "dur": 42, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089874, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089876, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089909, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089961, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144089964, "dur": 44, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144090020, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144090064, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144090067, "dur": 68, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144090161, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144090165, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144090203, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144090206, "dur": 45, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144090260, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144090262, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144090299, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144090362, "dur": 16565, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144106934, "dur": 37, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144106975, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144107055, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144107058, "dur": 3571, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144110653, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144110671, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144110713, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144110715, "dur": 45, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144110771, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144110776, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144110835, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144110837, "dur": 267, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144111108, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144111110, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144111183, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144111186, "dur": 5115, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144116320, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144116322, "dur": 112, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144116442, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144116444, "dur": 42, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144116490, "dur": 343, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144116848, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144116850, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144116892, "dur": 50, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144116944, "dur": 140, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117090, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117131, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117134, "dur": 57, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117193, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117195, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117227, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117228, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117292, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117333, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117429, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117460, "dur": 23, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117486, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117567, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117569, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117651, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117654, "dur": 230, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117887, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117889, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117936, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117939, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117976, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144117978, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118065, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118068, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118113, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118148, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118150, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118220, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118222, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118282, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118285, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118325, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118336, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118372, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118374, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118427, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118429, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118449, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118499, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118534, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118568, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118569, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118607, "dur": 10, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118618, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118662, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118690, "dur": 202, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118903, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118940, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144118942, "dur": 221, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119166, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119168, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119249, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119251, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119308, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119310, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119340, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119342, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119389, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119414, "dur": 41, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119466, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119468, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119500, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119520, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119590, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119593, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119626, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119628, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119654, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119707, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119761, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119809, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119811, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119857, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144119890, "dur": 180, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120075, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120098, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120122, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120168, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120174, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120237, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120238, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120266, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120268, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120297, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120348, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120381, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120441, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120475, "dur": 6, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120506, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120533, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120535, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120606, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120608, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120657, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120661, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120710, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120716, "dur": 135, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120863, "dur": 9, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120877, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120948, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120950, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144120987, "dur": 11, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121000, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121115, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121145, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121148, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121188, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121190, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121273, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121298, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121300, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121348, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121374, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121389, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121430, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121455, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121499, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121516, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121550, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121551, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121590, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121592, "dur": 232, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121866, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121869, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121907, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121909, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121944, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144121945, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144122027, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144122100, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144122135, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144122182, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144122214, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144122227, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144122261, "dur": 153, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144122419, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144122450, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144122452, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144122489, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144122526, "dur": 419, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144122987, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144122989, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144123029, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144123047, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144123136, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144123138, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144123180, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144123234, "dur": 83, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144123321, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144123330, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144123370, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144123372, "dur": 159, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144123535, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144123537, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144123561, "dur": 761, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144124428, "dur": 74, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144124505, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144124509, "dur": 31, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144124542, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144124546, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144124653, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144124655, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144124682, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144124721, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144124723, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144124768, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144124769, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144124800, "dur": 287, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144125090, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144125092, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144125128, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144125131, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144125174, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144125280, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144125283, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144125326, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144125361, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144125430, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144125432, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144125559, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144125591, "dur": 155, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144125749, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144125768, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144125835, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144125837, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144125971, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144126001, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144126032, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144126062, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144126069, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144126090, "dur": 489, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144126582, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144126616, "dur": 140, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144126760, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144126797, "dur": 268, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144127078, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144127100, "dur": 519, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144127626, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144127631, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144127662, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144127673, "dur": 54912, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144182595, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144182599, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144182636, "dur": 5324, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144187967, "dur": 1685, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144189659, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144189662, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144189695, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144189698, "dur": 114, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144189816, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144189847, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144189850, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144189946, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144189974, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144189976, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144190047, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144190076, "dur": 193, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144190272, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144190274, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144190302, "dur": 215, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144190521, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144190549, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144190553, "dur": 192, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144190749, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144190777, "dur": 1142, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144191925, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144191967, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144192039, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144192085, "dur": 130, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144192219, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144192221, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144192266, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144192373, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144192400, "dur": 420, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144192823, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144192825, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144192867, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144192869, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144192923, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144192949, "dur": 575, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144193529, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144193531, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144193579, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144193582, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144193617, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144193619, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144193724, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144193753, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144193755, "dur": 478, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144194237, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144194238, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144194278, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144194280, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144194323, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144194370, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144194438, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144194468, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144194470, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144194601, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144194603, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144194635, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144194637, "dur": 1267, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144195908, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144195910, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144195948, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144195951, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144196032, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144196034, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144196092, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144196095, "dur": 578, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144196677, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144196715, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144196718, "dur": 89, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144196811, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144196841, "dur": 6, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144196848, "dur": 1207, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144198059, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144198098, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144198100, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144198129, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144198131, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144198188, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144198215, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144198217, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144198247, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144198271, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144198371, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144198395, "dur": 365, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144198765, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144198792, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144198794, "dur": 844, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144199643, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144199645, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144199685, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144199687, "dur": 135, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144199827, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144199856, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144199858, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144199971, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144199973, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144200005, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144200007, "dur": 422, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144200434, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144200470, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144200505, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144200507, "dur": 236, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144200746, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144200775, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144200777, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144200836, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144200862, "dur": 106, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144200972, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144200995, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144201049, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144201078, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144201080, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144201119, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144201149, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144201178, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144201200, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144201223, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144201252, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144201274, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144201306, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144201342, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144201348, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144201383, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144201423, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144201425, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144201449, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144201481, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144201512, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144201563, "dur": 514, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202080, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202082, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202102, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202136, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202138, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202171, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202173, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202199, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202201, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202227, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202250, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202276, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202300, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202356, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202358, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202387, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202417, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202490, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202492, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202518, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202520, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202543, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202576, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202609, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202634, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202637, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202662, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202688, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202714, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202742, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202766, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202790, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202791, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202818, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202820, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202849, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202851, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202887, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202899, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202925, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202928, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202969, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144202971, "dur": 30, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144203004, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144203006, "dur": 94, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144203104, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144203141, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144203143, "dur": 68, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144203214, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144203216, "dur": 37, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144203260, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144203293, "dur": 287, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144203593, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144203629, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144203631, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144203657, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962144203681, "dur": 984578, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145188272, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145188305, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145188340, "dur": 24, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145188366, "dur": 10065, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145198437, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145198442, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145198486, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145198488, "dur": 20084, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145218582, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145218587, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145218674, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145218679, "dur": 50, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145218731, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145218733, "dur": 73272, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145292035, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145292042, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145292093, "dur": 34, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145292128, "dur": 4854, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145296993, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145296998, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145297032, "dur": 31, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145297066, "dur": 18452, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145315527, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145315530, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145315564, "dur": 3, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145315568, "dur": 1636, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145317211, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145317215, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145317264, "dur": 31, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145317297, "dur": 4624, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145321929, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145321932, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145321954, "dur": 14473, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145336436, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145336440, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145336462, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145336465, "dur": 834, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145337304, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145337307, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145337347, "dur": 60, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145337409, "dur": 642, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145338055, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145338057, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145338093, "dur": 394, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 9188, "tid": 12884901888, "ts": 1751962145338491, "dur": 9818, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 9188, "tid": 1850, "ts": 1751962145365070, "dur": 2070, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 9188, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 9188, "tid": 8589934592, "ts": 1751962144049705, "dur": 141235, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 9188, "tid": 8589934592, "ts": 1751962144190943, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 9188, "tid": 8589934592, "ts": 1751962144190950, "dur": 1572, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 9188, "tid": 1850, "ts": 1751962145367152, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 9188, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 9188, "tid": 4294967296, "ts": 1751962143963391, "dur": 1386559, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 9188, "tid": 4294967296, "ts": 1751962143968052, "dur": 52527, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 9188, "tid": 4294967296, "ts": 1751962145350179, "dur": 5420, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 9188, "tid": 4294967296, "ts": 1751962145353804, "dur": 244, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 9188, "tid": 4294967296, "ts": 1751962145355679, "dur": 13, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 9188, "tid": 1850, "ts": 1751962145367159, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751962144059242, "dur": 51, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751962144059322, "dur": 2441, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751962144061773, "dur": 1298, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751962144063214, "dur": 96, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751962144063311, "dur": 551, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751962144064797, "dur": 1560, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_85A7F339847C7C8F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751962144067409, "dur": 1608, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7CC9B7F9B9108300.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751962144070307, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751962144074635, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751962144075536, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751962144075616, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751962144063904, "dur": 21926, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751962144085845, "dur": 1252170, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751962145338017, "dur": 135, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751962145338152, "dur": 230, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751962145338578, "dur": 61, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751962145338670, "dur": 1514, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751962144063951, "dur": 21903, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144085881, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144085999, "dur": 1022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_B8BEB69DE53F9C41.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751962144087022, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144087167, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144087277, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144087383, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144087501, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144087582, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144087762, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144087830, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_C3A5EBA3221A6287.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751962144087970, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144088167, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751962144088257, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144088491, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144088628, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144088832, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144088941, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144089012, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144089147, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144089209, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144089352, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144089535, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144089710, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144089932, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144090005, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144090232, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144090312, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144090506, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144090626, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144090775, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144090832, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144092082, "dur": 1356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144093438, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144093893, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144094321, "dur": 1205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144095527, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144095958, "dur": 391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144096349, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144096797, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144098438, "dur": 560, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Graphs\\Matrix2ShaderProperty.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751962144097268, "dur": 1802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144099071, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144099480, "dur": 2325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144101805, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144102075, "dur": 383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144102458, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144102811, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144103157, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144105064, "dur": 2215, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.rendering.light-transport@9bd588f963c0\\Runtime\\UnifiedRayTracing\\Compute\\RadeonRays\\StringIDs.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751962144103486, "dur": 3933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144107419, "dur": 2019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144109507, "dur": 704, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\ReflectionFieldAccessor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751962144109438, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144110576, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144111346, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144111412, "dur": 1824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144114084, "dur": 778, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\float2x3.gen.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751962144113237, "dur": 1807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144115044, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144115911, "dur": 1014, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144116925, "dur": 600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144117526, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751962144117713, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144117800, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751962144118458, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144118674, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144118760, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751962144118901, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144119213, "dur": 658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751962144119872, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144120029, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751962144120170, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144120231, "dur": 1007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751962144121238, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144121505, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751962144121701, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144121758, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751962144122271, "dur": 545, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144122833, "dur": 690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1751962144123558, "dur": 135, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144124214, "dur": 59006, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1751962144188362, "dur": 1761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751962144190125, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144190278, "dur": 1958, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751962144192237, "dur": 1322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144193573, "dur": 2088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751962144195662, "dur": 1032, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144196699, "dur": 2038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751962144198741, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144198900, "dur": 2040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751962144200941, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144201060, "dur": 2245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751962144203306, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144203487, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144203616, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962144203806, "dur": 1112200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751962145316045, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751962145316014, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751962145316169, "dur": 1675, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751962145317848, "dur": 20174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144064104, "dur": 21952, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144086068, "dur": 707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_31AB2D3F1E4A3D66.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751962144086776, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144086890, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_57398B720CBF96BC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751962144086957, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144087058, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144087186, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144087293, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144087451, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144087609, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144087776, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_7251D83D80DD0453.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751962144087881, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144087942, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_88B40F493262C216.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751962144088077, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144088465, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144088692, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144088788, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144088850, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144089005, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144089066, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751962144089123, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751962144089173, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144089320, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144089505, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144089696, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144089819, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144089997, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144090176, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144090302, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144090515, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144090772, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144090834, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144092192, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144093456, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144093974, "dur": 788, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@a0f5d16b3c82\\Editor\\TMP\\PropertyDrawers\\GlyphRectPropertyDrawer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751962144093886, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144095093, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144096289, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144096782, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144098491, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Graphs\\SerializableVirtualTexture.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751962144097195, "dur": 2045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144099241, "dur": 2080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144101322, "dur": 2743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144104066, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144104753, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144105157, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144105568, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144105974, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144106450, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144106855, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144107421, "dur": 2717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144110138, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144110583, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144111467, "dur": 1787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144114273, "dur": 1220, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\bool4x2.gen.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751962144113255, "dur": 2529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144115855, "dur": 77, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144115933, "dur": 990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144116923, "dur": 1084, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144118008, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751962144118140, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144118225, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751962144118792, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144118975, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751962144119114, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144119180, "dur": 1613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751962144120794, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144121172, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144121435, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144121507, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751962144121728, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144122070, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751962144122594, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144122747, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144122886, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751962144123018, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144123080, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751962144123644, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144123809, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144123957, "dur": 3272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144127229, "dur": 61134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144188365, "dur": 2076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751962144190442, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144190708, "dur": 3272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751962144193982, "dur": 1098, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144195090, "dur": 2108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751962144197199, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144197319, "dur": 1985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751962144199305, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144199406, "dur": 3538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751962144202945, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962144203343, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.ConversionSystem.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751962144203761, "dur": 988766, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962145192574, "dur": 22868, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751962145192530, "dur": 24603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751962145219114, "dur": 222, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751962145220025, "dur": 77578, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751962145315983, "dur": 21011, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751962145315956, "dur": 21040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751962145337013, "dur": 930, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751962144064068, "dur": 21923, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144086001, "dur": 1023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_DD3CBC65A51B3B4A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751962144087025, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144087178, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_AF66D8329BCEF71E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751962144087344, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144087528, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144087598, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_BBA4FF1108562044.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751962144087732, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144088376, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144088626, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144088811, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144088884, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751962144088954, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144089163, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144089498, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144089665, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144089730, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144089813, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144089880, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144089963, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144090039, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144090334, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144090874, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751962144090941, "dur": 1434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144092376, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144093607, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144094043, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144095306, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144096472, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144096899, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144098373, "dur": 616, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@052d95cfe62a\\Editor\\Data\\Graphs\\DynamicValueMaterialSlot.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751962144097327, "dur": 2148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144099475, "dur": 1882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144101357, "dur": 2955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144104312, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144104689, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144105095, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144105517, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144105941, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144106387, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144106785, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144107217, "dur": 2340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144109558, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144109969, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144110422, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144110841, "dur": 1809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144112650, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144113058, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144113910, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144114590, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144114991, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144115419, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144115971, "dur": 953, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144116924, "dur": 1082, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144118007, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751962144118132, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144118194, "dur": 1094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751962144119289, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144119558, "dur": 1228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751962144120787, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144121106, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144121296, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144121527, "dur": 1315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144122843, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144122916, "dur": 2285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144125203, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751962144125365, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144125431, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751962144125817, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144125969, "dur": 1274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144127243, "dur": 63116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144190368, "dur": 2194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751962144192563, "dur": 444, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144193020, "dur": 3455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751962144196476, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144196664, "dur": 2019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751962144198684, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144199023, "dur": 2308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751962144201332, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144201488, "dur": 2618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751962144204107, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751962144204264, "dur": 1133723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144063984, "dur": 21885, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144085891, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144085996, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_36D94D0B0882E7EC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751962144086770, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144087075, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144087608, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144087760, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144087922, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144088165, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144088329, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751962144088505, "dur": 18878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751962144107384, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144107554, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144107672, "dur": 2148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144109820, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144110252, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144110689, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144111207, "dur": 1842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144113049, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144113450, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144113882, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144114525, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144114930, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144115341, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144115912, "dur": 1024, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144116937, "dur": 581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144117519, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751962144117704, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144117784, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751962144118369, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144118611, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751962144118810, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751962144119022, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144119139, "dur": 1519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751962144120659, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144120914, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751962144121175, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144121425, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751962144122053, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144122176, "dur": 673, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144122863, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751962144123000, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144123065, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751962144123459, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144123597, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144123654, "dur": 3588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144127242, "dur": 61337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144188588, "dur": 5532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751962144194121, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144194237, "dur": 2918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751962144197156, "dur": 1537, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144198720, "dur": 2283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751962144201004, "dur": 610, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144201734, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144201914, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144202154, "dur": 658, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144202837, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144202999, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144203321, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144203396, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144203613, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144203746, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144204257, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751962144204321, "dur": 1133693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144064040, "dur": 21841, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144085897, "dur": 864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_E7120EB995DA210C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751962144086762, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144086999, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144087088, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144087171, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144087254, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144087361, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144087454, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144087558, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144087706, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_982F16DC875BE786.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751962144087772, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144087934, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144088486, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144088643, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144088736, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144089159, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144089434, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144089701, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144089758, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144089968, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144090209, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144090547, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144090750, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144090837, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144090908, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144091957, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144092861, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144093276, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144093705, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144094141, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144095419, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144096211, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144096628, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144097079, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144098512, "dur": 536, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@59eb6f11d242\\Editor\\BurstDisassembler.Core.Wasm.info.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751962144098212, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144099223, "dur": 2048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144101272, "dur": 3122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144104394, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144104839, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144105244, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144105658, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144106084, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144106550, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144106957, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144107357, "dur": 1871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144109228, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144109664, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144110071, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144110532, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144110963, "dur": 1933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144112897, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144113320, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144114019, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144114992, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144115437, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144116184, "dur": 735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144116963, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144117522, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751962144117680, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144117766, "dur": 690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751962144118457, "dur": 550, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144119065, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751962144119272, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144119327, "dur": 1196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751962144120525, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144120771, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751962144121044, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144121118, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751962144121703, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144121924, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144121988, "dur": 862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144122850, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144122911, "dur": 1795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144124712, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144124776, "dur": 2449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144127227, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751962144127431, "dur": 60940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144188383, "dur": 2039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751962144190423, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144190593, "dur": 2190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751962144192784, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144192866, "dur": 1914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751962144194781, "dur": 450, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144195239, "dur": 5761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751962144201000, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144201136, "dur": 2552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751962144203689, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751962144203820, "dur": 1134200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144064091, "dur": 21914, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144086012, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_01F0438719F4637C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751962144086784, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144086950, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144087061, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144087241, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144087361, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144087584, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144087902, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144088508, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144088608, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144088662, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144088750, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144088827, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751962144088997, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144089537, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144089688, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144089744, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144089819, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144089975, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144090099, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144090158, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144090281, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144090369, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144090641, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751962144090734, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144090820, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144091990, "dur": 1289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144093279, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144093707, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144094138, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144094924, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144096047, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144096457, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144096877, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144097333, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144098477, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144099354, "dur": 2372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144101727, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144102485, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144102840, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144103187, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144103553, "dur": 1788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144105341, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144105759, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144106192, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144106637, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144107077, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144107928, "dur": 1844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144109773, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144110207, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144110928, "dur": 2006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144112934, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144113704, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144114496, "dur": 987, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\Tool\\AuthToken.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751962144114123, "dur": 1361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144115484, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144116355, "dur": 591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144116946, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144117519, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751962144117832, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144117941, "dur": 1724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751962144119666, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144119833, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144119953, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751962144120165, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144120253, "dur": 952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751962144121205, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144121504, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751962144121719, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144121812, "dur": 858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751962144122670, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144122860, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751962144123020, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144123088, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751962144123603, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144123802, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751962144123897, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144123952, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751962144124506, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144124624, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144124700, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751962144124871, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751962144125158, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144125344, "dur": 1888, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144127233, "dur": 61147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144188383, "dur": 2347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751962144190732, "dur": 654, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144191397, "dur": 2640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751962144194039, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144194192, "dur": 2227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751962144196420, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144196566, "dur": 2098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751962144198669, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144198835, "dur": 2743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751962144201579, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144202170, "dur": 631, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144202952, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144203175, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144203295, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144203498, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144203640, "dur": 591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751962144204266, "dur": 1133753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144064131, "dur": 21946, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144086084, "dur": 706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_F016717A46FE59BB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751962144086791, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144087001, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144087115, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144087297, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144087450, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_810264E72E8EDE2D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751962144087571, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144087754, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_91BF7E65A37C8C47.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751962144087858, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144087960, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144088067, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144088133, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1280E01E43015E29.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751962144088233, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144088493, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144088645, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144088708, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144088814, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144089011, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144089308, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751962144089375, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144089462, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144089530, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751962144089679, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144089845, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144090033, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144090105, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144090165, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144090333, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144090763, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144090830, "dur": 1144, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144091975, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144092850, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144093185, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144093660, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144094104, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144095320, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144096491, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144096935, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144098487, "dur": 506, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a55da47cc43f\\Runtime\\2D\\PixelPerfectCameraInternal.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751962144097443, "dur": 1669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144099112, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144099668, "dur": 2051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144101720, "dur": 2900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144104621, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144105013, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144105458, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144105871, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144106305, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144106717, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144107127, "dur": 1839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144108967, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144109362, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144109798, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144110623, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144111054, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144112175, "dur": 1874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144114049, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144114447, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144115305, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144115749, "dur": 140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144115920, "dur": 997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144116956, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144117517, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751962144117673, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144117759, "dur": 1870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751962144119630, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144119862, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144119918, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751962144120043, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144120098, "dur": 1853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751962144121952, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144122145, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144122223, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751962144122403, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144122468, "dur": 1138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751962144123607, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144123783, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144123951, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751962144124072, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144124130, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751962144124564, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144124704, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751962144124884, "dur": 1381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751962144126266, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144126442, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751962144126605, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751962144127064, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144127229, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751962144127407, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751962144127628, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962144127719, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751962144129082, "dur": 1059781, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751962145192879, "dur": 5955, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751962145192521, "dur": 6371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751962145198893, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962145199051, "dur": 16370, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751962145199049, "dur": 18084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751962145218639, "dur": 229, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751962145220030, "dur": 72519, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751962145315983, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751962145315963, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751962145316117, "dur": 21879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144064154, "dur": 21939, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144086102, "dur": 866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_AC251BD94F5FD3AE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751962144086969, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144087121, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144087236, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144087368, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144087510, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144087800, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CE693D9B51C79752.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751962144088165, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CE693D9B51C79752.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751962144088302, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751962144088467, "dur": 22528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751962144110997, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144111258, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144111356, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751962144111623, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144111742, "dur": 5049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751962144116792, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144116979, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751962144117085, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751962144117358, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144117513, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751962144117708, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144117870, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751962144118535, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144119023, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751962144119156, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144119252, "dur": 784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751962144120036, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144120308, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144120391, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144120513, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144120888, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751962144121098, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144121159, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751962144121896, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144122083, "dur": 754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144122837, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144122930, "dur": 4304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144127234, "dur": 61126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144188362, "dur": 2653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751962144191016, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144191160, "dur": 2163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751962144193324, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144197771, "dur": 302, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 8, "ts": 1751962144198073, "dur": 1546, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 8, "ts": 1751962144199620, "dur": 98, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.51f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 8, "ts": 1751962144193467, "dur": 6251, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144199719, "dur": 4055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751962144203774, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751962144203909, "dur": 1134091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144064188, "dur": 21916, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144086110, "dur": 803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_59C0854D1BDEB3BB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751962144086914, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144087059, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144087241, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144087319, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144087402, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144087527, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144087708, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_C7086B172735D898.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751962144088224, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144088487, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751962144088708, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144088791, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144088905, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144088973, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144089257, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144089386, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144089554, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144089698, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144089939, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144090029, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144090128, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144090181, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144090334, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144090615, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144090703, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144090805, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144090890, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144092048, "dur": 1109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144093157, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144093587, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144094005, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144094531, "dur": 1188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144095719, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144096124, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144096550, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144096970, "dur": 458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144098477, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a55da47cc43f\\Runtime\\2D\\Shadows\\ShadowProvider\\Providers\\ShadowShape2DProvider_SpriteRenderer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751962144097428, "dur": 1748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144099176, "dur": 2155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144101331, "dur": 3072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144104404, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144105120, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144105537, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144105937, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144106380, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144106786, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144107190, "dur": 2089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144109280, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144109978, "dur": 636, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@6279e2b7c485\\Runtime\\VisualScripting.Core\\Profiling\\ProfiledSegment.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751962144109691, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144110715, "dur": 1980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144112695, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144113219, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144114475, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144114987, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144115147, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144115836, "dur": 69, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144115916, "dur": 1014, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144116930, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144117525, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751962144117662, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144117734, "dur": 1023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751962144118757, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144118939, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144119007, "dur": 883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751962144119891, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144120043, "dur": 1023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751962144121066, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144121294, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144121375, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144121527, "dur": 699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144122227, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751962144122416, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144122497, "dur": 1185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751962144123683, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144123972, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_EDC8690F57C5BDFD.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751962144124039, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144124109, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751962144124287, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751962144124990, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144125197, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751962144125373, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751962144125847, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144126021, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751962144126189, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751962144126543, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144126714, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144127241, "dur": 63241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144190495, "dur": 1909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751962144192405, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144192560, "dur": 2156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751962144194721, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144194878, "dur": 2302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751962144197181, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144197347, "dur": 2102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751962144199450, "dur": 1156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144200617, "dur": 3503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751962144204121, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751962144204276, "dur": 1133706, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144064243, "dur": 21880, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144086131, "dur": 898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_172A8F2500200F04.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751962144087029, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144087173, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144087506, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144087801, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_72CE526178B0FB4C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751962144087856, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144088486, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144088630, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144088886, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144089010, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144089732, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144089911, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751962144089984, "dur": 656, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144090657, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14402696373757716438.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751962144090890, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144090980, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144092199, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144093360, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144093799, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144094244, "dur": 1600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144095844, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144096273, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144096708, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144097173, "dur": 2209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144099382, "dur": 1976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144101358, "dur": 2640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144105022, "dur": 2253, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Runtime\\GPUDriven\\GPUResidentDrawerResources.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751962144103998, "dur": 3990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144107989, "dur": 1733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144109723, "dur": 454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144110177, "dur": 436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144110614, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144111049, "dur": 2143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144113192, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144114204, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144114622, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144115024, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144115455, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144116031, "dur": 905, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144116936, "dur": 1069, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144118005, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751962144118615, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144118835, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144118995, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751962144119275, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144119507, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751962144120002, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144120157, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751962144120324, "dur": 1064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751962144121389, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144121607, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144121789, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751962144122007, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144122145, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751962144122667, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144122836, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144123074, "dur": 4176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144127250, "dur": 61137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144188397, "dur": 1861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751962144190259, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144190456, "dur": 3808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751962144194265, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144194371, "dur": 2137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751962144196509, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144196693, "dur": 2129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751962144198823, "dur": 1441, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144200273, "dur": 2383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751962144202657, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144202992, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144203071, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144203231, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144203465, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144203619, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751962144203827, "dur": 1134164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144064212, "dur": 21902, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144086120, "dur": 866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B76B86F6FFE10D96.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751962144086987, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144087164, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144087251, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144087505, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144087600, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144087972, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144088209, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144088414, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144088714, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144088888, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144089001, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144089123, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144089185, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144089307, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751962144089369, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144089482, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751962144089551, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144089761, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144089986, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144090510, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144090633, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751962144090824, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144091633, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144092580, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144093363, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144093824, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144094239, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144095430, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144095981, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144096715, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144097172, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144098238, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144099163, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144100500, "dur": 2911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144103412, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144103773, "dur": 1611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144105384, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144105815, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144106249, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144106654, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144107087, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144108430, "dur": 1508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144109939, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144110354, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144110843, "dur": 1819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144112662, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144113047, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144113445, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144113841, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144114272, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144114710, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144115098, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144115546, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144116491, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144116935, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144117525, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751962144117711, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144117768, "dur": 798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751962144118567, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144119025, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751962144119215, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751962144119933, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144120118, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751962144120633, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144121014, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144121106, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144121323, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144121525, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751962144121793, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751962144122289, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144122596, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144122865, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751962144123059, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144123125, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751962144123607, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144123752, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144123808, "dur": 3419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144127229, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751962144127358, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144127429, "dur": 60972, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144188407, "dur": 2323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751962144190731, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144190931, "dur": 3064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751962144193996, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144194169, "dur": 2236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751962144196406, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144196551, "dur": 2081, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751962144198632, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144198740, "dur": 2305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751962144201045, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144201391, "dur": 2255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751962144203650, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962144203780, "dur": 1112221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751962145316042, "dur": 6456, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751962145316004, "dur": 6495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751962145322549, "dur": 15462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144064267, "dur": 21866, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144086135, "dur": 882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_B52235DE51A256C2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751962144087018, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144087183, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_DF6F7FF917E347B3.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751962144087569, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144087716, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_AC0F081190478124.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751962144087954, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144088148, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144088250, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144088491, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144088605, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144088666, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144088850, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144088994, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144089229, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144089290, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751962144089458, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144089620, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144089725, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144089784, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144089960, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144090340, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144090519, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144090710, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144090837, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751962144090891, "dur": 1397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144092288, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144093428, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144093860, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144094263, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144095402, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144096579, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144096992, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144098522, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a55da47cc43f\\Runtime\\2D\\Light2DAuthoring.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751962144097739, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144099466, "dur": 2234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144104574, "dur": 548, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@2be5e7224a10\\Editor\\Camera\\ISerializedCamera.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751962144101701, "dur": 3422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144105123, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144105558, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144105980, "dur": 439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144106419, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144106837, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144107257, "dur": 1692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144108949, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144109703, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144110123, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144110591, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144110996, "dur": 2057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144113053, "dur": 428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144113481, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144113910, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144114321, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144114734, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144115138, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144115580, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144116292, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144116921, "dur": 622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144117544, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751962144117703, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144117774, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751962144117922, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144118047, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751962144118641, "dur": 532, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144119183, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144119303, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751962144119485, "dur": 534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144120025, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751962144120171, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144120229, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751962144120740, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144120897, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144120993, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751962144121142, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144121254, "dur": 758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751962144122012, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144122193, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144122874, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751962144123044, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144123152, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751962144123593, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144123776, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144123960, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751962144124085, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144124149, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751962144124684, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144124835, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144124893, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751962144125009, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144125090, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751962144125512, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144125731, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144125784, "dur": 1455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144127240, "dur": 63131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144190378, "dur": 2104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751962144192483, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144192685, "dur": 2133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751962144194819, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144194972, "dur": 2334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751962144197307, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144197463, "dur": 2648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751962144200115, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144200471, "dur": 2119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751962144202591, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144202723, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144202788, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751962144203001, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144203229, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144203327, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144203578, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144203631, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751962144203920, "dur": 1134065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751962145346299, "dur": 2140, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 9188, "tid": 1850, "ts": 1751962145368097, "dur": 2628, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 9188, "tid": 1850, "ts": 1751962145370790, "dur": 2154, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 9188, "tid": 1850, "ts": 1751962145362474, "dur": 11350, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}