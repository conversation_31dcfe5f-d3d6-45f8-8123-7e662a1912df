using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class GameManager : MonoBehaviour
{
    [Header("UI References")]
    public TextMeshProUGUI scoreText;
    public TextMeshProUGUI orbCountText;
    public GameObject winPanel;
    
    [Header("Game Settings")]
    public int totalOrbs = 12;
    public AudioClip winSound;
    
    private int currentScore = 0;
    private int orbsCollected = 0;
    private AudioSource audioSource;
    
    public static GameManager Instance { get; private set; }
    
    void Awake()
    {
        // Singleton pattern
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
            return;
        }
    }
    
    void Start()
    {
        // Get audio source
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null)
        {
            audioSource = gameObject.AddComponent<AudioSource>();
        }
        
        // Subscribe to orb collection events
        CollectibleOrb.OnOrbCollected += HandleOrbCollection;
        
        // Initialize UI
        UpdateUI();
        
        // Hide win panel initially
        if (winPanel != null)
        {
            winPanel.SetActive(false);
        }
        
        // Count total orbs in scene if not manually set
        if (totalOrbs <= 0)
        {
            totalOrbs = FindObjectsOfType<CollectibleOrb>().Length;
        }
    }
    
    void OnDestroy()
    {
        // Unsubscribe from events
        CollectibleOrb.OnOrbCollected -= HandleOrbCollection;
    }
    
    void HandleOrbCollection(int points)
    {
        currentScore += points;
        orbsCollected++;
        
        UpdateUI();
        
        // Check for win condition
        if (orbsCollected >= totalOrbs)
        {
            WinGame();
        }
    }
    
    void UpdateUI()
    {
        if (scoreText != null)
        {
            scoreText.text = "Score: " + currentScore.ToString();
        }
        
        if (orbCountText != null)
        {
            orbCountText.text = "Orbs: " + orbsCollected.ToString() + "/" + totalOrbs.ToString();
        }
    }
    
    void WinGame()
    {
        Debug.Log("You Win! All orbs collected!");
        
        // Play win sound
        if (winSound != null && audioSource != null)
        {
            audioSource.PlayOneShot(winSound);
        }
        
        // Show win panel
        if (winPanel != null)
        {
            winPanel.SetActive(true);
        }
        
        // Optional: Pause the game or show restart options
        // Time.timeScale = 0f; // Uncomment to pause the game
    }
    
    public void RestartGame()
    {
        Time.timeScale = 1f; // Resume game if it was paused
        UnityEngine.SceneManagement.SceneManager.LoadScene(UnityEngine.SceneManagement.SceneManager.GetActiveScene().name);
    }
    
    public int GetCurrentScore()
    {
        return currentScore;
    }
    
    public int GetOrbsCollected()
    {
        return orbsCollected;
    }
}
